import React from 'react';
import PropTypes from 'prop-types';
import { PageTransition as MotionPageTransition } from '../utils/motion';

/**
 * PageTransition component for adding smooth transitions between pages
 * This is a wrapper around the PageTransition component from utils/motion
 * to ensure it uses the same React instance as the rest of the application.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to be wrapped
 * @param {string|Object} props.variant - Animation variant name or custom variants object
 * @param {string|Object} props.transitionType - Transition type name or custom transition settings
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.style - Additional inline styles
 * @param {boolean} props.layoutId - Enable layout animations with a unique ID
 * @returns {React.ReactElement} The PageTransition component
 */
const PageTransition = (props) => {
  return <MotionPageTransition {...props} />;
};

PageTransition.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  transitionType: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  className: PropTypes.string,
  style: PropTypes.object,
  layoutId: PropTypes.string
};

export default PageTransition;
