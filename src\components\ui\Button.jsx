import React from 'react';
import PropTypes from 'prop-types';
import { Link } from 'react-router-dom';
import { motion } from '../../utils/motion';

/**
 * Enhanced Button component with animations and various styles
 * 
 * @param {Object} props - Component props
 * @param {string} props.variant - Button variant (primary, secondary, accent, success, warning, error, ghost, link)
 * @param {string} props.size - Button size (xs, sm, md, lg, xl)
 * @param {boolean} props.outline - Whether the button has an outline style
 * @param {boolean} props.rounded - Whether the button has fully rounded corners
 * @param {boolean} props.fullWidth - Whether the button takes full width
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {boolean} props.loading - Whether the button is in loading state
 * @param {string} props.to - Link destination (for Link buttons)
 * @param {string} props.href - URL for anchor buttons
 * @param {string} props.type - Button type (button, submit, reset)
 * @param {Function} props.onClick - Click handler
 * @param {React.ReactNode} props.leftIcon - Icon to display on the left
 * @param {React.ReactNode} props.rightIcon - Icon to display on the right
 * @param {string} props.className - Additional CSS classes
 * @param {React.ReactNode} props.children - Button content
 * @returns {React.ReactElement} The Button component
 */
const Button = ({
  variant = 'primary',
  size = 'md',
  outline = false,
  rounded = false,
  fullWidth = false,
  disabled = false,
  loading = false,
  to,
  href,
  type = 'button',
  onClick,
  leftIcon,
  rightIcon,
  className = '',
  children,
  ...rest
}) => {
  // Base classes
  const baseClasses = 'btn';
  
  // Size classes
  const sizeClasses = {
    xs: 'btn-xs',
    sm: 'btn-sm',
    md: '',
    lg: 'btn-lg',
    xl: 'btn-xl',
  };
  
  // Variant classes
  const getVariantClasses = () => {
    if (outline) {
      return `btn-outline btn-outline-${variant}`;
    }
    
    if (variant === 'ghost') {
      return 'btn-ghost';
    }
    
    if (variant === 'link') {
      return 'btn-link';
    }
    
    return `btn-${variant}`;
  };
  
  // Additional classes
  const additionalClasses = [
    rounded ? 'rounded-full' : '',
    fullWidth ? 'w-full' : '',
    disabled || loading ? 'btn-disabled' : '',
    className,
  ].filter(Boolean).join(' ');
  
  // Combine all classes
  const buttonClasses = [
    baseClasses,
    sizeClasses[size],
    getVariantClasses(),
    additionalClasses,
  ].filter(Boolean).join(' ');
  
  // Button content with loading state and icons
  const buttonContent = (
    <>
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {leftIcon && !loading && <span className="mr-2">{leftIcon}</span>}
      {children}
      {rightIcon && <span className="ml-2">{rightIcon}</span>}
    </>
  );
  
  // Animation props
  const animationProps = {
    whileHover: { scale: disabled || loading ? 1 : 1.02 },
    whileTap: { scale: disabled || loading ? 1 : 0.98 },
    transition: { duration: 0.1 }
  };
  
  // Render as Link if 'to' prop is provided
  if (to) {
    return (
      <motion.div {...animationProps}>
        <Link
          to={to}
          className={buttonClasses}
          {...rest}
        >
          {buttonContent}
        </Link>
      </motion.div>
    );
  }
  
  // Render as anchor if 'href' prop is provided
  if (href) {
    return (
      <motion.a
        href={href}
        className={buttonClasses}
        {...animationProps}
        {...rest}
      >
        {buttonContent}
      </motion.a>
    );
  }
  
  // Render as button
  return (
    <motion.button
      type={type}
      className={buttonClasses}
      disabled={disabled || loading}
      onClick={onClick}
      {...animationProps}
      {...rest}
    >
      {buttonContent}
    </motion.button>
  );
};

Button.propTypes = {
  variant: PropTypes.oneOf(['primary', 'secondary', 'accent', 'success', 'warning', 'error', 'ghost', 'link']),
  size: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  outline: PropTypes.bool,
  rounded: PropTypes.bool,
  fullWidth: PropTypes.bool,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  to: PropTypes.string,
  href: PropTypes.string,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  onClick: PropTypes.func,
  leftIcon: PropTypes.node,
  rightIcon: PropTypes.node,
  className: PropTypes.string,
  children: PropTypes.node.isRequired,
};

export default Button;
