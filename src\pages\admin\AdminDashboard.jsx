import React, { useState, useEffect } from 'react';
import { Link, useNavigate, Outlet, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../context/AuthContext';
import {
  FiHome, FiUsers, FiDatabase, FiBarChart2, FiSettings,
  FiLogOut, FiMenu, FiX, FiChevronDown, FiChevronRight
} from 'react-icons/fi';

const AdminDashboard = () => {
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState(null);

  // Check if user is admin
  useEffect(() => {
    if (!currentUser || currentUser.role !== 'admin') {
      toast.error('You do not have permission to access this page');
      navigate('/');
    }
  }, [currentUser, navigate]);

  // Close mobile sidebar when location changes
  useEffect(() => {
    setIsMobileSidebarOpen(false);
  }, [location]);

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/');
    toast.success('Logged out successfully');
  };

  // Toggle submenu
  const toggleSubmenu = (menu) => {
    if (activeSubmenu === menu) {
      setActiveSubmenu(null);
    } else {
      setActiveSubmenu(menu);
    }
  };

  // Check if a route is active
  const isRouteActive = (route) => {
    return location.pathname === route || location.pathname.startsWith(`${route}/`);
  };

  // Navigation items
  const navItems = [
    {
      name: 'Dashboard',
      icon: <FiHome className="h-5 w-5" />,
      path: '/admin',
      exact: true
    },
    {
      name: 'Users',
      icon: <FiUsers className="h-5 w-5" />,
      path: '/admin/users',
      submenu: [
        { name: 'All Users', path: '/admin/users' },
        { name: 'Add User', path: '/admin/users/add' },
        { name: 'User Roles', path: '/admin/users/roles' }
      ]
    },
    {
      name: 'Models',
      icon: <FiDatabase className="h-5 w-5" />,
      path: '/admin/models',
      submenu: [
        { name: 'All Models', path: '/admin/models' },
        { name: 'Add Model', path: '/admin/models/add' },
        { name: 'Categories', path: '/admin/models/categories' }
      ]
    },
    {
      name: 'Analytics',
      icon: <FiBarChart2 className="h-5 w-5" />,
      path: '/admin/analytics',
      submenu: [
        { name: 'Overview', path: '/admin/analytics' },
        { name: 'Downloads', path: '/admin/analytics/downloads' },
        { name: 'Revenue', path: '/admin/analytics/revenue' }
      ]
    },
    {
      name: 'Settings',
      icon: <FiSettings className="h-5 w-5" />,
      path: '/admin/settings'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      {/* Mobile sidebar toggle */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
          className="p-2 rounded-md bg-white dark:bg-gray-800 shadow-md text-gray-700 dark:text-gray-300"
          aria-label="Toggle sidebar"
        >
          {isMobileSidebarOpen ? (
            <FiX className="h-6 w-6" />
          ) : (
            <FiMenu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 z-40 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}`}
      >
        {/* Sidebar header */}
        <div className="h-16 flex items-center justify-between px-4 border-b border-gray-200 dark:border-gray-700">
          <Link to="/" className="flex items-center">
            <span className="text-xl font-bold text-blue-600 dark:text-blue-400">3DSKETCHUP</span>
            <span className="ml-1 text-xs font-semibold text-gray-600 dark:text-gray-400">ADMIN</span>
          </Link>
          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="p-1 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 lg:hidden"
            aria-label="Toggle sidebar"
          >
            <FiChevronRight className={`h-5 w-5 transform transition-transform ${isSidebarOpen ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Sidebar content */}
        <div className="py-4 overflow-y-auto">
          <div className="px-4 mb-6">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 font-bold text-lg">
                {currentUser?.name.charAt(0).toUpperCase()}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900 dark:text-white">{currentUser?.name}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Administrator</p>
              </div>
            </div>
          </div>

          <nav className="space-y-1 px-2">
            {navItems.map((item) => (
              <div key={item.name}>
                {item.submenu ? (
                  <div>
                    <button
                      onClick={() => toggleSubmenu(item.name)}
                      className={`flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        isRouteActive(item.path)
                          ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700/30'
                      }`}
                    >
                      <div className="flex items-center">
                        {item.icon}
                        <span className="ml-3">{item.name}</span>
                      </div>
                      <FiChevronDown
                        className={`h-4 w-4 transition-transform ${
                          activeSubmenu === item.name ? 'transform rotate-180' : ''
                        }`}
                      />
                    </button>
                    {activeSubmenu === item.name && (
                      <div className="mt-1 space-y-1 pl-10">
                        {item.submenu.map((subItem) => (
                          <Link
                            key={subItem.name}
                            to={subItem.path}
                            className={`block px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                              location.pathname === subItem.path
                                ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                                : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700/30'
                            }`}
                          >
                            {subItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    to={item.path}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      (item.exact && location.pathname === item.path) ||
                      (!item.exact && isRouteActive(item.path))
                        ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                        : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700/30'
                    }`}
                  >
                    {item.icon}
                    <span className="ml-3">{item.name}</span>
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Logout button */}
          <div className="px-4 mt-8">
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded-md transition-colors"
            >
              <FiLogOut className="h-5 w-5" />
              <span className="ml-3">Logout</span>
            </button>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className={`transition-all duration-300 ${isSidebarOpen ? 'lg:ml-64' : 'ml-0'}`}>
        <main className="p-4 md:p-8">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;
