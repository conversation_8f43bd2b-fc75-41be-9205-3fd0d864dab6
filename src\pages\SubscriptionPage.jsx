import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import { usePayment } from '../context/PaymentContext';
import Header from '../components/Header';
import Footer from '../components/Footer';
import SubscriptionPlans from '../components/subscription/SubscriptionPlans';
import PaymentForm from '../components/subscription/PaymentForm';
import SubscriptionDashboard from '../components/subscription/SubscriptionDashboard';
import Button from '../components/ui/Button';
import Alert from '../components/ui/Alert';

const SubscriptionPage = () => {
  const { currentUser, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const [step, setStep] = useState('plans'); // plans, payment, dashboard
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [subscription, setSubscription] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch user subscription data
  useEffect(() => {
    const fetchSubscription = async () => {
      if (!isAuthenticated) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // In a real app, this would be an API call
        // For demo purposes, we'll simulate a subscription
        await new Promise(resolve => setTimeout(resolve, 1000));



        // Get subscription data from context
        const { subscription } = usePayment();

        if (subscription) {
          setSubscription({
            planId: subscription.plan,
            billingCycle: subscription.interval,
            nextBillingDate: subscription.endDate,
            usage: {
              downloads: currentUser?.downloadCredits || 0,
              uploads: currentUser?.uploadCount || 0
            }
          });
        } else {
          // If no subscription data, set default free plan
          setSubscription({
            planId: 'free',
            billingCycle: 'monthly',
            nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            usage: {
              downloads: currentUser?.downloadCredits || 0,
              uploads: currentUser?.uploadCount || 0
            }
          });
        }

        setStep('dashboard');
      } catch (err) {
        setError('Failed to load subscription data. Please try again.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscription();
  }, [isAuthenticated]);

  // Handle plan selection
  const handleSelectPlan = (plan) => {
    setSelectedPlan(plan);

    if (!isAuthenticated) {
      // Redirect to login if not authenticated
      navigate('/login', { state: { from: '/subscription', selectedPlan: plan } });
      return;
    }

    setStep('payment');
  };

  // Handle payment completion
  const handlePaymentComplete = (paymentDetails) => {
    // In a real app, this would update the subscription in the database
    setSubscription({
      planId: paymentDetails.planId,
      billingCycle: paymentDetails.billingCycle,
      nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days from now
      usage: {
        downloads: 0,
        uploads: 0
      },
      paymentMethods: [
        {
          id: 'pm_new',
          type: paymentDetails.paymentMethod === 'credit-card' ? 'card' : 'paypal',
          brand: paymentDetails.paymentMethod === 'credit-card' ? 'Visa' : undefined,
          last4: paymentDetails.paymentMethod === 'credit-card' ? '4242' : undefined,
          expMonth: paymentDetails.paymentMethod === 'credit-card' ? '12' : undefined,
          expYear: paymentDetails.paymentMethod === 'credit-card' ? '2024' : undefined,
          isDefault: true
        }
      ],
      billingHistory: [
        {
          date: new Date().toISOString(),
          description: `${paymentDetails.planId.charAt(0).toUpperCase() + paymentDetails.planId.slice(1)} Plan (${paymentDetails.billingCycle === 'monthly' ? 'Monthly' : 'Annual'})`,
          amount: selectedPlan.price,
          status: 'paid',
          receiptUrl: '#'
        }
      ]
    });

    setStep('dashboard');
  };

  // Handle upgrade subscription
  const handleUpgradeSubscription = () => {
    setStep('plans');
  };

  // Handle cancel subscription
  const handleCancelSubscription = () => {
    // In a real app, this would update the subscription in the database
    setSubscription({
      ...subscription,
      planId: 'free',
      billingCycle: 'monthly',
      nextBillingDate: null,
      paymentMethods: subscription.paymentMethods,
      billingHistory: [
        {
          date: new Date().toISOString(),
          description: 'Subscription Cancelled',
          amount: 0,
          status: 'cancelled',
          receiptUrl: '#'
        },
        ...subscription.billingHistory
      ]
    });
  };

  // Handle update payment method
  const handleUpdatePayment = (paymentMethodId) => {
    // In a real app, this would open a payment method update form
    alert(`Update payment method ${paymentMethodId}`);
  };

  // Render content based on current step
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="max-w-3xl mx-auto py-8">
          <Alert
            variant="error"
            title="Error"
            dismissible
            onDismiss={() => setError('')}
          >
            {error}
          </Alert>

          <div className="mt-6 text-center">
            <Button
              variant="primary"
              size="lg"
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </div>
        </div>
      );
    }

    switch (step) {
      case 'plans':
        return (
          <div className="max-w-6xl mx-auto py-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Choose Your Subscription Plan
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                Select the plan that best fits your needs and unlock premium features
              </p>
            </div>

            <SubscriptionPlans onSelectPlan={handleSelectPlan} />
          </div>
        );

      case 'payment':
        return (
          <div className="max-w-3xl mx-auto py-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Complete Your Subscription
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Enter your payment details to subscribe to the {selectedPlan.planId.charAt(0).toUpperCase() + selectedPlan.planId.slice(1)} plan
              </p>
            </div>

            <PaymentForm
              selectedPlan={selectedPlan}
              onBack={() => setStep('plans')}
              onComplete={handlePaymentComplete}
            />
          </div>
        );

      case 'dashboard':
        return (
          <div className="max-w-6xl mx-auto py-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Subscription Management
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Manage your subscription, payment methods, and billing history
              </p>
            </div>

            <SubscriptionDashboard
              subscription={subscription}
              onUpgrade={handleUpgradeSubscription}
              onCancel={handleCancelSubscription}
              onUpdatePayment={handleUpdatePayment}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="flex-grow pt-16">
        {renderContent()}
      </main>

      <Footer />
    </div>
  );
};

export default SubscriptionPage;
