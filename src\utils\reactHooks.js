/**
 * React Hooks and APIs Utility
 *
 * This utility ensures that we're using the same React hooks and APIs instance throughout the application.
 * It helps prevent "Invalid Hook Call" errors that occur when multiple React instances are used.
 */

// Import React directly - this should work with proper Vite configuration
import React from 'react';

// Ensure React is available and has the hooks we need
if (!React || !React.useState) {
  console.error('React hooks are not available:', React);
  throw new Error('React hooks are not available. Check React installation and configuration.');
}

// Export hooks explicitly to avoid undefined issues
export const useState = React.useState;
export const useEffect = React.useEffect;
export const useContext = React.useContext;
export const useReducer = React.useReducer;
export const useCallback = React.useCallback;
export const useMemo = React.useMemo;
export const useRef = React.useRef;
export const useImperativeHandle = React.useImperativeHandle;
export const useLayoutEffect = React.useLayoutEffect;
export const useDebugValue = React.useDebugValue;
export const useDeferredValue = React.useDeferredValue;
export const useTransition = React.useTransition;
export const useId = React.useId;

// React 18 hooks
export const useSyncExternalStore = React.useSyncExternalStore;
export const useInsertionEffect = React.useInsertionEffect;

// Export other React APIs that were in reactSingleton
export const memo = React.memo;
export const createContext = React.createContext;
export const forwardRef = React.forwardRef;
export const lazy = React.lazy;
export const Suspense = React.Suspense;
export const Fragment = React.Fragment;
export const StrictMode = React.StrictMode;
export const cloneElement = React.cloneElement;
export const createElement = React.createElement;
export const isValidElement = React.isValidElement;
export const Children = React.Children;
export const Component = React.Component;
export const PureComponent = React.PureComponent;

// Export a hooks object for destructuring
const hooks = {
  // Hooks
  useState,
  useEffect,
  useContext,
  useReducer,
  useCallback,
  useMemo,
  useRef,
  useImperativeHandle,
  useLayoutEffect,
  useDebugValue,
  useDeferredValue,
  useTransition,
  useId,
  useSyncExternalStore,
  useInsertionEffect,

  // APIs
  memo,
  createContext,
  forwardRef,
  lazy,
  Suspense,
  Fragment,
  StrictMode,
  cloneElement,
  createElement,
  isValidElement,
  Children,
  Component,
  PureComponent
};

export default hooks;
