import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import dotenv from 'dotenv';
import fileUpload from 'express-fileupload';
import path from 'path';
import fs from 'fs';
import { connectDB } from './config/db.js';

// Import routes
import authRoutes from './routes/authRoutes.js';
import modelRoutes from './routes/modelRoutes.js';
import userRoutes from './routes/userRoutes.js';
import categoryRoutes from './routes/categoryRoutes.js';
import paymentRoutes from './routes/paymentRoutes.js';
import reviewRoutes from './routes/reviewRoutes.js';
import chatRoutes from './routes/chatRoutes.js';
import statsRoutes from './routes/statsRoutes.js';
import newsletterRoutes from './routes/newsletterRoutes.js';
import activityRoutes from './routes/activityRoutes.js';
import mongodbRoutes from './routes/mongodbRoutes.js';
import aiAssistantRoutes from './routes/aiAssistantRoutes.js';
import virtualShowroomRoutes from './routes/virtualShowroomRoutes.js';
import smartCollectionRoutes from './routes/smartCollectionRoutes.js';
import searchRoutes from './routes/searchRoutes.js';
import adminRoutes from './routes/adminRoutes.js';
import downloadRoutes from './routes/downloadRoutes.js';
import extensionsRoutes from './routes/extensionsRoutes.js';
import versionRoutes from './routes/versionRoutes.js';
import imageRoutes from './routes/imageRoutes.js';

// Load environment variables
dotenv.config();

// Debug: Log environment variables to verify they're loaded
console.log('MongoDB URI loaded:', process.env.MONGODB_URI ? 'Yes' : 'No');
console.log('MongoDB URI preview:', process.env.MONGODB_URI ? process.env.MONGODB_URI.substring(0, 20) + '...' : 'Not found');
console.log('Gemini API Key loaded:', process.env.GEMINI_API_KEY ? 'Yes' : 'No');
console.log('Gemini API Key preview:', process.env.GEMINI_API_KEY ? process.env.GEMINI_API_KEY.substring(0, 10) + '...' : 'Not found');

// Initialize express app
const app = express();

// Connect to database
connectDB();

// Create upload directories if they don't exist
const uploadDirs = ['./uploads', './uploads/models', './uploads/images', './uploads/images/chat', './uploads/previews'];
uploadDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Middleware
// Configure CORS with specific options
app.use(cors({
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173', 'http://************:5173'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400 // 24 hours
}));

// Add OPTIONS handling for preflight requests
app.options('*', cors());

// Parse JSON and URL-encoded bodies
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// File upload middleware
app.use(fileUpload({
  createParentPath: true,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB max file size
  },
  abortOnLimit: true,
  useTempFiles: true,
  tempFileDir: './uploads/temp/'
}));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Static folder for uploads
app.use('/uploads', express.static('uploads'));

// Debug middleware for login requests
app.use('/api/auth/login', (req, res, next) => {
  console.log('=== LOGIN REQUEST DEBUG ===');
  console.log('Headers:', req.headers);
  console.log('Content-Type:', req.headers['content-type']);
  console.log('Body:', req.body);
  console.log('Raw body type:', typeof req.body);
  console.log('========================');
  next();
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/models', modelRoutes);
app.use('/api/users', userRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/newsletter', newsletterRoutes);
app.use('/api/activity', activityRoutes);
app.use('/api/mongodb', mongodbRoutes);
app.use('/api/ai', aiAssistantRoutes);
app.use('/api/showrooms', virtualShowroomRoutes);
app.use('/api/collections/smart', smartCollectionRoutes);
app.use('/api/search', searchRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/download', downloadRoutes);
app.use('/api/extensions', extensionsRoutes);
app.use('/api/version', versionRoutes);
app.use('/api/images', imageRoutes);

// Root route
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to 3DSKETCHUP.NET API',
    version: '1.0.0',
    status: 'online',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API Health check endpoint (for frontend)
app.get('/api/health-check', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Debug endpoint for checking connection issues
app.get('/debug', (req, res) => {
  res.json({
    headers: req.headers,
    ip: req.ip,
    ips: req.ips,
    protocol: req.protocol,
    secure: req.secure,
    originalUrl: req.originalUrl,
    path: req.path,
    hostname: req.hostname,
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware with detailed logging
app.use((err, req, res, next) => {
  // Log detailed error information
  console.error('Server Error:', {
    message: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    ip: req.ip,
    timestamp: new Date().toISOString()
  });

  // Determine appropriate status code
  const statusCode = err.statusCode || 500;

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: err.message || 'Server Error',
    path: req.path,
    timestamp: new Date().toISOString(),
    // Only include stack trace in development
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// Handle 404 routes with detailed logging
app.use((req, res) => {
  // Log 404 requests
  console.warn('404 Not Found:', {
    path: req.path,
    method: req.method,
    ip: req.ip,
    timestamp: new Date().toISOString()
  });

  res.status(404).json({
    success: false,
    error: 'Route not found',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Start server
const PORT = process.env.PORT || 5002;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
  console.log(`Server URL: http://localhost:${PORT}`);
  console.log(`API URL: http://localhost:${PORT}/api`);
});

// Global error handlers for better stability

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.error('Unhandled Promise Rejection:', {
    message: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString()
  });

  // In production, we'll log but not exit to maintain service
  if (process.env.NODE_ENV === 'production') {
    console.error('Unhandled rejection in production - server continuing');
  } else {
    // In development, exit to make errors obvious
    console.error('Exiting due to unhandled promise rejection');
    process.exit(1);
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', {
    message: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString()
  });

  // Always exit on uncaught exceptions as the process state may be corrupted
  console.error('Exiting due to uncaught exception');
  process.exit(1);
});

// Log when process is about to exit
process.on('exit', (code) => {
  console.log(`Process is about to exit with code: ${code}`);
});

// Handle SIGTERM for graceful shutdown (e.g., when running in containers)
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  // Close server, DB connections, etc.
  process.exit(0);
});
