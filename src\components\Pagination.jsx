import React from 'react';
import { motion } from 'framer-motion';
import ReactPaginate from 'react-paginate';
import { FiChevronLeft as ChevronLeftIcon, FiChevronRight as ChevronRightIcon } from 'react-icons/fi';

const Pagination = ({ pageCount, currentPage, onPageChange }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="flex justify-center mt-12"
    >
      <div className="glass-card p-4 rounded-3xl border border-white/20 shadow-professional">
        <ReactPaginate
          previousLabel={
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <ChevronLeftIcon className="h-5 w-5" />
            </motion.div>
          }
          nextLabel={
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <ChevronRightIcon className="h-5 w-5" />
            </motion.div>
          }
          breakLabel={
            <div className="flex items-center justify-center w-10 h-10 text-gray-500 dark:text-gray-400 font-bold">
              ...
            </div>
          }
          breakClassName={'relative inline-flex items-center mx-1'}
          pageCount={pageCount}
          marginPagesDisplayed={2}
          pageRangeDisplayed={3}
          onPageChange={onPageChange}
          forcePage={currentPage}
          containerClassName={'flex items-center space-x-2'}
          pageClassName={'relative inline-flex items-center mx-1'}
          pageLinkClassName={'flex items-center justify-center w-10 h-10 glass-card border border-white/10 text-sm font-bold text-gray-700 dark:text-gray-200 hover:bg-white/20 hover:scale-110 transition-all duration-300 rounded-xl shadow-md hover:shadow-lg'}
          previousClassName={'relative inline-flex items-center mx-1'}
          nextClassName={'relative inline-flex items-center mx-1'}
          activeClassName={'active'}
          activeLinkClassName={'flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-xl shadow-lg border-2 border-white/20 scale-110'}
          disabledClassName={'opacity-50 cursor-not-allowed'}
          disabledLinkClassName={'cursor-not-allowed hover:scale-100'}
        />
      </div>
    </motion.div>
  );
};

export default Pagination;
