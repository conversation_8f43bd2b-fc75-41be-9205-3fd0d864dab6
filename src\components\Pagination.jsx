import React from 'react';
import ReactPaginate from 'react-paginate';
import { FiChevronLeft as ChevronLeftIcon, FiChevronRight as ChevronRightIcon } from 'react-icons/fi';

const Pagination = ({ pageCount, currentPage, onPageChange }) => {
  return (
    <ReactPaginate
      previousLabel={<ChevronLeftIcon className="h-5 w-5" />}
      nextLabel={<ChevronRightIcon className="h-5 w-5" />}
      breakLabel={'...'}
      breakClassName={'relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300'}
      pageCount={pageCount}
      marginPagesDisplayed={2}
      pageRangeDisplayed={3}
      onPageChange={onPageChange}
      forcePage={currentPage}
      containerClassName={'flex justify-center mt-8'}
      pageClassName={'relative inline-flex items-center mx-1'}
      pageLinkClassName={'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-700 rounded-md'}
      previousClassName={'relative inline-flex items-center mx-1'}
      previousLinkClassName={'relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-700 rounded-md'}
      nextClassName={'relative inline-flex items-center mx-1'}
      nextLinkClassName={'relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-700 rounded-md'}
      breakLinkClassName={'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-200 dark:hover:bg-gray-700 rounded-md'}
      activeClassName={'active'}
      activeLinkClassName={'relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-600 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-700 dark:border-blue-600 dark:hover:bg-blue-800 rounded-md'}
      disabledClassName={'opacity-50 cursor-not-allowed'}
      disabledLinkClassName={'cursor-not-allowed'}
    />
  );
};

export default Pagination;
