import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useModel } from '../context/ModelContext';
import Header from '../components/Header';
import Footer from '../components/Footer';
import ModelCard from '../components/ModelCard';
import Pagination from '../components/Pagination';
import FilterSidebar from '../components/FilterSidebar';
import { FiFilter, FiX } from 'react-icons/fi';
import { motion } from 'framer-motion';

const CategoryPage = () => {
  const { category } = useParams();
  const { models, categories, loading } = useModel();
  const [filteredModels, setFilteredModels] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    subcategory: [],
    format: [],
    isPremium: null,
    minRating: 0,
    maxRating: 5,
    sortBy: 'newest'
  });
  const navigate = useNavigate();

  const modelsPerPage = 12;

  // Get category display name
  const getCategoryDisplayName = () => {
    if (!category) {
      return 'All Models';
    }

    switch(category) {
      case 'interior':
        return 'Interior Scenes';
      case 'exterior':
        return 'Exterior Scenes';
      case 'landscape':
        return 'Landscape/Garden';
      case 'models':
        return 'Models/Objects';
      default:
        return 'Category Not Found';
    }
  };

  // Filter models based on category and other filters
  useEffect(() => {
    if (!models || !Array.isArray(models) || models.length === 0) return;

    let result = [...models]; // Create a copy of the array

    // Filter by category (only if category parameter exists)
    if (category) {
      switch(category) {
        case 'interior':
          result = result.filter(model => {
            if (!model.category) return false;
            const cat = String(model.category).toLowerCase();
            return (cat === 'residential' && model.subcategory === 'Interior') ||
                   (cat === 'interior') ||
                   cat.includes('interior');
          });
          break;
        case 'exterior':
          result = result.filter(model => {
            if (!model.category) return false;
            const cat = String(model.category).toLowerCase();
            return (cat === 'exterior') ||
                   (cat === 'residential' && model.subcategory === 'Exterior') ||
                   cat.includes('exterior');
          });
          break;
        case 'landscape':
          result = result.filter(model => {
            if (!model.category) return false;
            const cat = String(model.category).toLowerCase();
            return (cat === 'landscape/garden') ||
                   cat.includes('landscape') ||
                   cat.includes('garden');
          });
          break;
        case 'models':
          result = result.filter(model => {
            if (!model.category) return false;
            const cat = String(model.category).toLowerCase();
            return (cat === 'furniture') ||
                   (cat === 'flower/shrub/bush') ||
                   (cat === 'other') ||
                   (cat === 'models/objects');
          });
          break;
        default:
          // If category not found, redirect to 404
          navigate('/not-found');
      }
    }
    // If no category parameter, show all models (no filtering by category)

    // Apply additional filters
    if (filters.subcategory && filters.subcategory.length > 0) {
      result = result.filter(model => model.subcategory && filters.subcategory.includes(model.subcategory));
    }

    if (filters.format && filters.format.length > 0) {
      result = result.filter(model => model.format && filters.format.includes(model.format));
    }

    if (filters.isPremium !== null) {
      result = result.filter(model => {
        // Handle case where isPremium might be undefined
        return model.isPremium === filters.isPremium;
      });
    }

    result = result.filter(model => {
      // Check if model.rating exists and is a valid number
      const rating = model.rating ? parseFloat(model.rating) : 0;
      return !isNaN(rating) &&
        rating >= filters.minRating &&
        rating <= filters.maxRating;
    });

    // Sort results
    switch(filters.sortBy) {
      case 'newest':
        result.sort((a, b) => {
          const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
          const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
          return dateB - dateA;
        });
        break;
      case 'oldest':
        result.sort((a, b) => {
          const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
          const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
          return dateA - dateB;
        });
        break;
      case 'popular':
        result.sort((a, b) => {
          const downloadsA = a.downloads || 0;
          const downloadsB = b.downloads || 0;
          return downloadsB - downloadsA;
        });
        break;
      case 'rating':
        result.sort((a, b) => {
          const ratingA = a.rating ? parseFloat(a.rating) : 0;
          const ratingB = b.rating ? parseFloat(b.rating) : 0;
          return ratingB - ratingA;
        });
        break;
      default:
        break;
    }

    setFilteredModels(result);
  }, [category, models, filters, navigate]);

  // Get current models for pagination
  const indexOfLastModel = currentPage * modelsPerPage;
  const indexOfFirstModel = indexOfLastModel - modelsPerPage;
  const currentModels = filteredModels.slice(indexOfFirstModel, indexOfLastModel);

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Toggle filter sidebar on mobile
  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="flex-grow container mx-auto px-4 pt-24 pb-12">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Mobile filter toggle */}
          <div className="md:hidden flex justify-between items-center mb-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {getCategoryDisplayName()}
            </h1>
            <button
              onClick={toggleFilter}
              className="btn btn-secondary flex items-center"
            >
              <FiFilter className="mr-2" />
              Filters
            </button>
          </div>

          {/* Filter sidebar - desktop */}
          <div className="hidden md:block w-64 flex-shrink-0">
            <div className="sticky top-24">
              <FilterSidebar
                filters={filters}
                onFilterChange={handleFilterChange}
                categories={categories}
              />
            </div>
          </div>

          {/* Filter sidebar - mobile */}
          {isFilterOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden">
              <motion.div
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ duration: 0.3 }}
                className="absolute right-0 top-0 h-full w-80 bg-white dark:bg-gray-800 p-4 overflow-y-auto"
              >
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Filters</h3>
                  <button onClick={toggleFilter} className="text-gray-500">
                    <FiX className="h-6 w-6" />
                  </button>
                </div>
                <FilterSidebar
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  categories={categories}
                  onClose={toggleFilter}
                />
              </motion.div>
            </div>
          )}

          {/* Main content */}
          <div className="flex-grow">
            <div className="hidden md:flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {getCategoryDisplayName()}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {filteredModels.length} models found
              </p>
            </div>

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="loader"></div>
              </div>
            ) : filteredModels.length > 0 ? (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {currentModels.map(model => (
                    <ModelCard key={model.id} model={model} />
                  ))}
                </div>

                <Pagination
                  itemsPerPage={modelsPerPage}
                  totalItems={filteredModels.length}
                  paginate={paginate}
                  currentPage={currentPage}
                />
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <p className="text-xl text-gray-600 dark:text-gray-400 mb-4">
                  No models found in this category with the selected filters.
                </p>
                <button
                  onClick={() => setFilters({
                    subcategory: [],
                    format: [],
                    isPremium: null,
                    minRating: 0,
                    maxRating: 5,
                    sortBy: 'newest'
                  })}
                  className="btn btn-primary"
                >
                  Reset Filters
                </button>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CategoryPage;
