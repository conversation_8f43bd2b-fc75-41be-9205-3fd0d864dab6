import React, { lazy, useEffect, Suspense } from 'react';

// Import other dependencies
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { AuthProvider } from './context/AuthContext';
import { ModelProvider } from './context/ModelContext';
import { PaymentProvider } from './context/PaymentContext';
import { Toaster } from 'react-hot-toast';
import ToastProvider from './components/ToastSystem';
import ProtectedRoute from './components/ProtectedRoute';
import Chatbot from './components/Chatbot';
import ErrorBoundary from './components/ErrorBoundary';
import ClientErrorBoundary from './components/ClientErrorBoundary';
// import SimpleNotification from './components/SimpleNotification';
// HMR components removed to fix WebSocket issues
import './App.css';

// Loading component with skeleton UI
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
    <div className="w-full max-w-7xl mx-auto px-4">
      {/* Header skeleton */}
      <div className="h-16 mb-8 flex items-center justify-between">
        <div className="w-40 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        <div className="flex space-x-4">
          <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>

      {/* Content skeleton */}
      <div className="space-y-8">
        <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-48 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Prefetch functionality is now integrated directly in AppRoutes

// Lazy load pages with improved performance
const Home = lazy(() => import('./pages/Home'));
const NewHome = lazy(() => import('./pages/NewHome'));
const Login = lazy(() => import('./pages/Login'));
const Register = lazy(() => import('./pages/Register'));
const ForgotPassword = lazy(() => import('./pages/ForgotPassword'));
const ResetPassword = lazy(() => import('./pages/ResetPassword'));
const ModelDetail = lazy(() => import('./pages/ModelDetail'));
const Profile = lazy(() => import('./pages/Profile'));
const SavedModels = lazy(() => import('./pages/SavedModels'));
const Downloads = lazy(() => import('./pages/Downloads'));
const Subscription = lazy(() => import('./pages/Subscription'));
const UploadModel = lazy(() => import('./pages/UploadModel'));
const CategoryPage = lazy(() => import('./pages/CategoryPage'));
const SearchResults = lazy(() => import('./pages/SearchResults'));
const SearchPage = lazy(() => import('./pages/SearchPage'));
const VirtualShowrooms = lazy(() => import('./pages/VirtualShowrooms'));
const SmartCollections = lazy(() => import('./pages/SmartCollections'));
const Warehouse = lazy(() => import('./pages/Warehouse'));
const Plugins = lazy(() => import('./pages/Plugins'));
const FeaturesDemo = lazy(() => import('./pages/FeaturesDemo'));
const NotFound = lazy(() => import('./pages/NotFound'));

// Admin pages
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
const DashboardOverview = lazy(() => import('./pages/admin/DashboardOverview'));
const UserManagement = lazy(() => import('./pages/admin/UserManagement'));
const AddUser = lazy(() => import('./pages/admin/AddUser'));
const UserRoles = lazy(() => import('./pages/admin/UserRoles'));
const ModelManagement = lazy(() => import('./pages/admin/ModelManagement'));
const AddModel = lazy(() => import('./pages/admin/AddModel'));
const ModelCategories = lazy(() => import('./pages/admin/ModelCategories'));
const Analytics = lazy(() => import('./pages/admin/Analytics'));
const DownloadAnalytics = lazy(() => import('./pages/admin/DownloadAnalytics'));
const RevenueAnalytics = lazy(() => import('./pages/admin/RevenueAnalytics'));
const Settings = lazy(() => import('./pages/admin/Settings'));

// Information pages
const About = lazy(() => import('./pages/About'));
const Contact = lazy(() => import('./pages/Contact'));
const Terms = lazy(() => import('./pages/Terms'));
const Privacy = lazy(() => import('./pages/Privacy'));

// Test pages
const ImageSearchTest = lazy(() => import('./components/test/ImageSearchTest'));
const AdminAddModel = lazy(() => import('./pages/AdminAddModel'));
const AdminAddModelSimple = lazy(() => import('./pages/AdminAddModelSimple'));

// AppRoutes component converted to functional component
function AppRoutes() {
  const location = window.location.pathname;

  // Using useEffect from the same React instance
  useEffect(() => {
    // Enhanced prefetching strategy
    const prefetchRoutes = () => {
      const currentPath = window.location.pathname;
      const prefetchQueue = [];

      // Determine which routes to prefetch based on current path
      if (currentPath === '/') {
        // On homepage, prefetch most common navigation paths
        prefetchQueue.push(
          () => import('./pages/ModelDetail'),
          () => import('./pages/CategoryPage'),
          () => import('./pages/SearchResults')
        );
      } else if (currentPath.startsWith('/model/')) {
        // On model detail page, prefetch related pages
        prefetchQueue.push(
          () => import('./pages/SearchResults'),
          () => import('./pages/CategoryPage'),
          () => import('./components/ModelViewer')
        );
      } else if (currentPath === '/login') {
        // On login page, prefetch auth-related pages
        prefetchQueue.push(
          () => import('./pages/Register'),
          () => import('./pages/ForgotPassword'),
          () => import('./pages/Profile')
        );
      } else if (currentPath.startsWith('/category/')) {
        // On category page, prefetch model detail
        prefetchQueue.push(
          () => import('./pages/ModelDetail')
        );
      } else if (currentPath === '/profile') {
        // On profile page, prefetch user-related pages
        prefetchQueue.push(
          () => import('./pages/SavedModels'),
          () => import('./pages/Downloads'),
          () => import('./pages/Subscription')
        );
      }

      // Execute prefetching with priority and throttling
      if (prefetchQueue.length > 0) {
        // Immediately prefetch the first route (highest priority)
        prefetchQueue[0]();

        // Schedule the rest with increasing delays
        prefetchQueue.slice(1).forEach((prefetchFn, index) => {
          setTimeout(() => {
            if (document.visibilityState === 'visible') {
              prefetchFn();
            }
          }, (index + 1) * 1000); // Stagger prefetching by 1 second intervals
        });
      }
    };

    // Initial prefetch
    prefetchRoutes();

    // Set up prefetch on idle
    const idleCallback = 'requestIdleCallback' in window
      ? window.requestIdleCallback(prefetchRoutes, { timeout: 2000 })
      : setTimeout(prefetchRoutes, 1000);

    // Clean up
    return () => {
      if ('requestIdleCallback' in window && idleCallback) {
        window.cancelIdleCallback(idleCallback);
      } else {
        clearTimeout(idleCallback);
      }
    };
  }, [location]);

  return (
    <Routes>
      <Route path="/" element={<NewHome />} />
      <Route path="/old" element={<Home />} />
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/reset-password/:token" element={<ResetPassword />} />
      <Route path="/model/:id" element={<ModelDetail />} />
      <Route path="/profile" element={
        <ProtectedRoute>
          <Profile />
        </ProtectedRoute>
      } />
      <Route path="/saved-models" element={
        <ProtectedRoute>
          <SavedModels />
        </ProtectedRoute>
      } />
      <Route path="/downloads" element={
        <ProtectedRoute>
          <Downloads />
        </ProtectedRoute>
      } />
      <Route path="/subscription" element={
        <ProtectedRoute>
          <Subscription />
        </ProtectedRoute>
      } />
      <Route path="/upload" element={
        <ProtectedRoute
          adminOnly={true}
          requiredPermission="upload"
          unauthorizedMessage="Only administrators can upload models"
        >
          <UploadModel />
        </ProtectedRoute>
      } />
      {/* Category Routes */}
      <Route path="/models" element={<CategoryPage />} />
      <Route path="/category/:category" element={<CategoryPage />} />
      <Route path="/search" element={<SearchPage />} />
      <Route path="/search-results" element={<SearchResults />} />
      <Route path="/showrooms" element={<VirtualShowrooms />} />
      <Route path="/collections" element={<SmartCollections />} />
      <Route path="/warehouse" element={<Warehouse />} />
      <Route path="/plugins" element={<Plugins />} />
      <Route path="/features-demo" element={<FeaturesDemo />} />
      {/* Admin Routes */}
      <Route path="/admin" element={
        <ProtectedRoute adminOnly={true}>
          <AdminDashboard />
        </ProtectedRoute>
      }>
        <Route index element={<DashboardOverview />} />
        <Route path="users" element={<UserManagement />} />
        <Route path="users/add" element={<AddUser />} />
        <Route path="users/roles" element={<UserRoles />} />
        <Route path="models" element={<ModelManagement />} />
        <Route path="models/add" element={<AddModel />} />
        <Route path="models/categories" element={<ModelCategories />} />
        <Route path="analytics" element={<Analytics />} />
        <Route path="analytics/downloads" element={<DownloadAnalytics />} />
        <Route path="analytics/revenue" element={<RevenueAnalytics />} />
        <Route path="settings" element={<Settings />} />
      </Route>
      {/* Information pages */}
      <Route path="/about" element={<About />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />

      {/* Test pages */}
      <Route path="/test/image-search" element={<ImageSearchTest />} />
      <Route path="/admin/add-model" element={
        <ProtectedRoute adminOnly={true}>
          <AdminAddModel />
        </ProtectedRoute>
      } />
      <Route path="/admin/add-model-simple" element={
        <ProtectedRoute adminOnly={true}>
          <AdminAddModelSimple />
        </ProtectedRoute>
      } />

      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

// Convert to functional component to use hooks properly
function App() {
  // Using useEffect from the same React instance
  useEffect(() => {
    // Add dark mode class to html element based on localStorage or system preference
    const darkMode = localStorage.getItem('darkMode');
    if (darkMode === 'true' || (!darkMode && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Completely unregister all service workers and don't register new ones
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(registrations => {
        for (let registration of registrations) {
          registration.unregister();
          console.log('Service worker unregistered');
        }
      }).catch(error => {
        console.error('Service worker unregistration failed:', error);
      });

      // Clear any service worker caches
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            caches.delete(cacheName);
            console.log(`Cache ${cacheName} deleted`);
          });
        });
      }
    }
  }, []);

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <ToastProvider>
          <AuthProvider>
            <ModelProvider>
              <PaymentProvider>
                <Router>
                  <div className="flex flex-col min-h-screen transition-colors duration-200">
                    {/* Enhanced Toast notifications */}
                    <Toaster
                      position="top-right"
                      toastOptions={{
                        duration: 5000,
                        style: {
                          background: '#363636',
                          color: '#fff',
                        },
                        success: {
                          duration: 3000,
                          iconTheme: {
                            primary: '#10B981',
                            secondary: '#FFFFFF',
                          },
                        },
                        error: {
                          duration: 4000,
                          iconTheme: {
                            primary: '#EF4444',
                            secondary: '#FFFFFF',
                          },
                        },
                      }}
                    />

                  <Suspense fallback={<LoadingFallback />}>
                    <ErrorBoundary>
                      <AppRoutes />
                    </ErrorBoundary>
                  </Suspense>

                  {/* Chatbot component wrapped in ClientErrorBoundary */}
                  <ClientErrorBoundary>
                    <Chatbot />
                  </ClientErrorBoundary>

                  {/* Simple notification component - Disabled for clean UI */}
                  {/* <ClientErrorBoundary>
                    <SimpleNotification
                      message="Website đang hoạt động bình thường. Một số tính năng backend có thể bị hạn chế."
                      type="info"
                      dismissible={true}
                    />
                  </ClientErrorBoundary> */}

                  {/* HMR components removed to fix WebSocket issues */}
                </div>
              </Router>
            </PaymentProvider>
          </ModelProvider>
        </AuthProvider>
        </ToastProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;
