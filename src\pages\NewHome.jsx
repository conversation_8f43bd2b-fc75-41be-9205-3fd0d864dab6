import React, { useState, useEffect, lazy, Suspense } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Slider from 'react-slick';
import { FiSearch, FiDownload, FiUsers, FiDatabase, FiLoader } from 'react-icons/fi';
import { FaRegLightbulb, FaRegBuilding, FaTree, FaCouch } from 'react-icons/fa';
import Header from '../components/Header';
import Footer from '../components/Footer';
import PageTransition from '../components/PageTransition';
import NewFeaturesAnnouncement, { FeatureHighlights } from '../components/NewFeaturesAnnouncement';

import { statsAPI, modelsAPI } from '../utils/api';
import mongoService from '../services/mongoService';

const NewHome = () => {
  const [featuredModels, setFeaturedModels] = useState([]);
  const [stats, setStats] = useState({
    models: 0,
    downloads: 0,
    users: 0,
    categories: 0
  });


  useEffect(() => {
    // Load featured models and fetch statistics
    const loadData = async () => {
      try {
        // Fetch featured models from API
        const modelsResponse = await modelsAPI.getModels({
          sort: 'downloads',
          limit: 6,
          isPremium: true
        });

        if (modelsResponse && modelsResponse.data && modelsResponse.data.success) {
          console.log('Successfully fetched featured models:', modelsResponse.data);
          setFeaturedModels(modelsResponse.data.data);
        } else {
          console.error('Failed to fetch featured models');
          setFeaturedModels([]);
        }

        // Try to fetch statistics from MongoDB first
        try {
          const mongoResponse = await mongoService.getStatistics();

          if (mongoResponse && mongoResponse.success) {
            console.log('Successfully fetched real statistics from MongoDB:', mongoResponse);
            const realStats = mongoResponse.data;

            // Start with zeros and animate to real values
            let currentStats = {
              models: 0,
              downloads: 0,
              users: 0,
              categories: 0
            };

            // Set up interval to animate stats
            const interval = setInterval(() => {
              // Update each stat by a small increment
              currentStats = {
                models: Math.min(currentStats.models + Math.ceil(realStats.models / 20), realStats.models),
                downloads: Math.min(currentStats.downloads + Math.ceil(realStats.downloads / 20), realStats.downloads),
                users: Math.min(currentStats.users + Math.ceil(realStats.users / 20), realStats.users),
                categories: Math.min(currentStats.categories + Math.ceil(realStats.categories / 20), realStats.categories)
              };

              // Update state with current values
              setStats({
                models: currentStats.models,
                downloads: currentStats.downloads,
                users: currentStats.users,
                categories: currentStats.categories
              });

              // Check if all values have reached their targets
              if (
                currentStats.models >= realStats.models &&
                currentStats.downloads >= realStats.downloads &&
                currentStats.users >= realStats.users &&
                currentStats.categories >= realStats.categories
              ) {
                clearInterval(interval);
              }
            }, 50);

            return () => clearInterval(interval);
          }
        } catch (mongoError) {
          console.warn('Failed to fetch statistics from MongoDB, falling back to API:', mongoError);
        }

        // Fallback to regular API if MongoDB fails
        const response = await statsAPI.getSiteStats();

        if (response && response.data && response.data.success) {
          console.log('Successfully fetched real statistics from API:', response.data);
          const realStats = response.data.data;

          // Start with zeros and animate to real values
          let currentStats = {
            models: 0,
            downloads: 0,
            users: 0,
            categories: 0
          };

          // Animate stats
          const interval = setInterval(() => {
            const increment = {
              models: Math.ceil(realStats.totalModels / 30),
              downloads: Math.ceil(realStats.totalDownloads / 30),
              users: Math.ceil(realStats.totalUsers / 30),
              categories: Math.ceil(realStats.totalCategories / 30)
            };

            setStats(prev => ({
              models: prev.models + increment.models >= realStats.totalModels ? realStats.totalModels : prev.models + increment.models,
              downloads: prev.downloads + increment.downloads >= realStats.totalDownloads ? realStats.totalDownloads : prev.downloads + increment.downloads,
              users: prev.users + increment.users >= realStats.totalUsers ? realStats.totalUsers : prev.users + increment.users,
              categories: prev.categories + increment.categories >= realStats.totalCategories ? realStats.totalCategories : prev.categories + increment.categories
            }));

            // Update current stats for next check
            currentStats = { ...stats };

            // Check if all values have reached their targets
            if (
              currentStats.models >= realStats.totalModels &&
              currentStats.downloads >= realStats.totalDownloads &&
              currentStats.users >= realStats.totalUsers &&
              currentStats.categories >= realStats.totalCategories
            ) {
              clearInterval(interval);
            }
          }, 50);

          return () => clearInterval(interval);
        } else {
          console.error('Failed to fetch statistics from API');
          setStats({
            models: 0,
            downloads: 0,
            users: 0,
            categories: 0
          });
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setFeaturedModels([]);
        setStats({
          models: 0,
          downloads: 0,
          users: 0,
          categories: 0
        });
      }
    };

    // Call the async function
    const cleanup = loadData();

    // Return cleanup function
    return () => {
      if (cleanup && typeof cleanup === 'function') {
        cleanup();
      }
    };
  }, []);

  // Slider settings
  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    arrows: false
  };

  // Category cards
  const categories = [
    {
      name: 'Interior Scenes',
      icon: <FaRegLightbulb className="text-4xl mb-4 text-blue-500" />,
      description: 'High-quality interior scenes for architectural visualization',
      link: '/category/interior'
    },
    {
      name: 'Exterior Scenes',
      icon: <FaRegBuilding className="text-4xl mb-4 text-blue-500" />,
      description: 'Realistic exterior scenes for your architectural projects',
      link: '/category/exterior'
    },
    {
      name: 'Landscape/Garden',
      icon: <FaTree className="text-4xl mb-4 text-blue-500" />,
      description: 'Beautiful landscape and garden models for outdoor design',
      link: '/category/landscape'
    },
    {
      name: 'Furniture & Objects',
      icon: <FaCouch className="text-4xl mb-4 text-blue-500" />,
      description: 'Detailed furniture and object models for interior design',
      link: '/category/models'
    }
  ];

  // Loading component for Suspense
  const LoadingSpinner = () => (
    <div className="flex items-center justify-center min-h-screen">
      <FiLoader className="w-10 h-10 text-blue-500 animate-spin" />
    </div>
  );

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      {/* New Features Announcement will be moved to a better position */}

      <PageTransition>

      {/* Enhanced Hero Section */}
      <section className="relative hero-gradient text-white overflow-hidden header-offset min-h-screen flex items-center">
        <div className="hero-overlay"></div>
        <div className="absolute inset-0 opacity-10">
          <div className="animate-float absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
          <div className="animate-float absolute top-40 right-20 w-32 h-32 bg-accent-400/20 rounded-full blur-2xl" style={{animationDelay: '1s'}}></div>
          <div className="animate-float absolute bottom-20 left-1/4 w-24 h-24 bg-primary-400/20 rounded-full blur-xl" style={{animationDelay: '2s'}}></div>
        </div>
        <div className="container mx-auto px-4 py-20 md:py-32 relative z-10">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="text-5xl md:text-6xl lg:text-7xl font-bold mb-6 font-heading leading-tight"
              >
                <span className="block">Premium</span>
                <span className="block bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">3D Models</span>
                <span className="block">for Your Projects</span>
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed"
              >
                Discover high-quality 3D models, scenes, and assets for architectural visualization and design
              </motion.p>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <Link
                  to="/models"
                  className="interactive-button px-8 py-4 bg-white text-blue-700 hover:bg-blue-50 rounded-xl font-semibold text-lg shadow-xl hover:shadow-2xl"
                >
                  <span className="flex items-center justify-center">
                    Browse Models
                    <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                </Link>
                <Link
                  to="/register"
                  className="interactive-button glass-effect px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/20"
                >
                  Sign Up Free
                </Link>
              </motion.div>
            </div>
            <div className="md:w-1/2">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.7 }}
                className="relative"
              >
                <div className="bg-white p-2 rounded-xl shadow-2xl overflow-hidden">
                  <Slider {...sliderSettings}>
                    {featuredModels.map((model, index) => (
                      <div key={model._id || model.id || `featured-model-${index}`} className="outline-none">
                        <img
                          src={model.imageUrl}
                          alt={model.title}
                          className="w-full h-64 md:h-80 object-cover rounded-lg"
                        />
                        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 rounded-b-lg">
                          <h3 className="text-white text-lg font-semibold">{model.title}</h3>
                          <p className="text-blue-200 text-sm">{model.category}</p>
                        </div>
                      </div>
                    ))}
                  </Slider>
                </div>
                <div className="absolute -bottom-5 -right-5 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg transform rotate-3">
                  <span className="font-bold">Premium Quality</span>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Wave divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto">
            <path fill="#f9fafb" className="dark:fill-gray-900" fillOpacity="1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 md:p-8">
              <h2 className="text-2xl font-bold text-center mb-6 text-gray-800 dark:text-white">Find the Perfect 3D Model</h2>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search for models, scenes, objects..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                  Search
                </button>
              </div>
              <div className="mt-4 flex flex-wrap gap-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">Popular:</span>
                <Link to="/search?q=modern+interior" className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">Modern Interior</Link>
                <Link to="/search?q=kitchen" className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">Kitchen</Link>
                <Link to="/search?q=office" className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">Office</Link>
                <Link to="/search?q=garden" className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">Garden</Link>
                <Link to="/search?q=furniture" className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">Furniture</Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* New Features Announcement Section */}
      <section className="py-12 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <div className="container mx-auto px-4">
          <NewFeaturesAnnouncement />
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">Browse by Category</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Explore our extensive collection of 3D models organized by categories
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {categories.map((category, index) => (
              <motion.div
                key={`category-${category.name}-${index}`}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="interactive-card card-gradient p-8 text-center group"
              >
                <div className="flex justify-center mb-6">
                  <div className="p-4 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl text-white group-hover:scale-110 transition-transform duration-300">
                    {category.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-800 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">{category.name}</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">{category.description}</p>
                <Link
                  to={category.link}
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-semibold group-hover:translate-x-1 transition-all duration-300"
                >
                  Explore
                  <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Feature Highlights */}
          <div className="mt-16">
            <div className="text-center mb-12">
              <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">⚡ Tính Năng Nổi Bật</h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Khám phá các tính năng mạnh mẽ của nền tảng 3DSKETCHUP.NET
              </p>
            </div>
            <FeatureHighlights />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl md:text-5xl font-bold mb-2">{stats.models.toLocaleString()}</div>
              <div className="text-blue-200">3D Models</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold mb-2">{stats.downloads.toLocaleString()}</div>
              <div className="text-blue-200">Downloads</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold mb-2">{stats.users.toLocaleString()}</div>
              <div className="text-blue-200">Users</div>
            </div>
            <div>
              <div className="text-4xl md:text-5xl font-bold mb-2">{stats.categories}</div>
              <div className="text-blue-200">Categories</div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Models Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">Featured Models</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Discover our handpicked selection of premium 3D models
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredModels.map((model, index) => (
              <motion.div
                key={model._id || model.id || `featured-model-list-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="relative">
                  <img
                    src={model.imageUrl}
                    alt={model.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                    {model.format}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">{model.title}</h3>
                  <div className="flex items-center mb-3">
                    <span className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded text-xs text-gray-700 dark:text-gray-300">
                      {model.category}
                    </span>
                    {model.subcategory && (
                      <span className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded text-xs text-gray-700 dark:text-gray-300 ml-2">
                        {model.subcategory}
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                    {model.description}
                  </p>
                  <div className="flex justify-between items-center">
                    {(model._id || model.id) ? (
                      <Link
                        to={`/model/${model._id || model.id}`}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                      >
                        View Details
                      </Link>
                    ) : (
                      <span className="text-gray-400 font-medium">No ID Available</span>
                    )}
                    <div className="flex items-center text-gray-500 dark:text-gray-400 text-sm">
                      <FiDownload className="mr-1" />
                      <span>250+</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              to="/models"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
            >
              View All Models
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Elevate Your 3D Projects?</h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of professionals who trust 3DSKETCHUP.NET for high-quality 3D models
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                to="/register"
                className="px-8 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium text-lg transition-colors"
              >
                Create Free Account
              </Link>
              <Link
                to="/pricing"
                className="px-8 py-4 bg-transparent border-2 border-white hover:bg-white/10 rounded-lg font-medium text-lg transition-colors"
              >
                View Pricing
              </Link>
            </div>
          </div>
        </div>
      </section>

      </PageTransition>



      <Footer />
    </div>
  );
};

export default NewHome;
