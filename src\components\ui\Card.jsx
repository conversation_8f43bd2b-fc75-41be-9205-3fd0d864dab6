import React from 'react';
import PropTypes from 'prop-types';
import { motion } from '../../utils/motion';

/**
 * Card component with animation and various styles
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Card content
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.hoverable - Whether the card has hover effects
 * @param {boolean} props.clickable - Whether the card is clickable
 * @param {Function} props.onClick - Click handler
 * @param {boolean} props.animate - Whether to animate the card
 * @param {string} props.variant - Card variant (default, bordered, flat)
 * @returns {React.ReactElement} The Card component
 */
const Card = ({
  children,
  className = '',
  hoverable = false,
  clickable = false,
  onClick,
  animate = true,
  variant = 'default',
  ...rest
}) => {
  // Base classes
  const baseClasses = 'card';
  
  // Variant classes
  const variantClasses = {
    default: '',
    bordered: 'border border-gray-200 dark:border-gray-700 shadow-sm',
    flat: 'shadow-none',
  };
  
  // Additional classes
  const additionalClasses = [
    hoverable ? 'hover-lift' : '',
    clickable ? 'cursor-pointer' : '',
    className,
  ].filter(Boolean).join(' ');
  
  // Combine all classes
  const cardClasses = [
    baseClasses,
    variantClasses[variant],
    additionalClasses,
  ].filter(Boolean).join(' ');
  
  // Animation props
  const animationProps = animate ? {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.3 },
    whileHover: hoverable ? { y: -5, boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' } : {},
  } : {};
  
  return (
    <motion.div
      className={cardClasses}
      onClick={clickable ? onClick : undefined}
      {...animationProps}
      {...rest}
    >
      {children}
    </motion.div>
  );
};

/**
 * Card.Header component
 */
const CardHeader = ({ children, className = '', ...rest }) => {
  const classes = `card-header ${className}`;
  
  return (
    <div className={classes} {...rest}>
      {children}
    </div>
  );
};

/**
 * Card.Body component
 */
const CardBody = ({ children, className = '', ...rest }) => {
  const classes = `card-body ${className}`;
  
  return (
    <div className={classes} {...rest}>
      {children}
    </div>
  );
};

/**
 * Card.Footer component
 */
const CardFooter = ({ children, className = '', ...rest }) => {
  const classes = `card-footer ${className}`;
  
  return (
    <div className={classes} {...rest}>
      {children}
    </div>
  );
};

/**
 * Card.Image component
 */
const CardImage = ({ src, alt = '', className = '', ...rest }) => {
  const classes = `w-full object-cover ${className}`;
  
  return (
    <img src={src} alt={alt} className={classes} {...rest} />
  );
};

/**
 * Card.Title component
 */
const CardTitle = ({ children, className = '', ...rest }) => {
  const classes = `text-xl font-bold text-gray-900 dark:text-white mb-2 ${className}`;
  
  return (
    <h3 className={classes} {...rest}>
      {children}
    </h3>
  );
};

/**
 * Card.Subtitle component
 */
const CardSubtitle = ({ children, className = '', ...rest }) => {
  const classes = `text-sm text-gray-600 dark:text-gray-400 mb-4 ${className}`;
  
  return (
    <p className={classes} {...rest}>
      {children}
    </p>
  );
};

// Add sub-components to Card
Card.Header = CardHeader;
Card.Body = CardBody;
Card.Footer = CardFooter;
Card.Image = CardImage;
Card.Title = CardTitle;
Card.Subtitle = CardSubtitle;

// PropTypes
Card.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  hoverable: PropTypes.bool,
  clickable: PropTypes.bool,
  onClick: PropTypes.func,
  animate: PropTypes.bool,
  variant: PropTypes.oneOf(['default', 'bordered', 'flat']),
};

CardHeader.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

CardBody.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

CardFooter.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

CardImage.propTypes = {
  src: PropTypes.string.isRequired,
  alt: PropTypes.string,
  className: PropTypes.string,
};

CardTitle.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

CardSubtitle.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

export default Card;
