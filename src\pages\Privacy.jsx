import React from 'react';
import { motion } from 'framer-motion';
import { FiShield, FiDatabase, FiUsers, FiGlobe, FiLock, FiCookie, FiAlertCircle } from 'react-icons/fi';
import Header from '../components/Header';
import Footer from '../components/Footer';
import PageTransition from '../components/PageTransition';

const Privacy = () => {
  // Last updated date
  const lastUpdated = 'January 15, 2023';
  
  // Privacy policy sections
  const sections = [
    {
      id: 'information-collection',
      title: 'Information We Collect',
      icon: <FiDatabase />,
      content: `
        We collect several types of information from and about users of our website, including:
        
        - Personal information: Name, email address, postal address, phone number, and payment information when you register, 
          subscribe to our service, or make a purchase.
        
        - Profile information: Your username, password, profile picture, preferences, and any other information you choose to 
          provide in your account profile.
        
        - Usage data: Information about how you use our website, including your browsing actions, search queries, download history, 
          and interaction with models and other content.
        
        - Technical data: IP address, browser type and version, time zone setting, browser plug-in types and versions, operating 
          system and platform, device information, and other technology on the devices you use to access our website.
        
        - Location data: General location information based on your IP address.
      `
    },
    {
      id: 'information-use',
      title: 'How We Use Your Information',
      icon: <FiUsers />,
      content: `
        We use the information we collect for various purposes, including:
        
        - To provide, maintain, and improve our services.
        - To process transactions and send related information, including confirmations, invoices, and receipts.
        - To personalize your experience and deliver content relevant to your interests.
        - To send administrative information, such as updates to our terms, conditions, and policies.
        - To provide customer support and respond to inquiries.
        - To send promotional communications, such as special offers or newsletters (with your consent).
        - To monitor and analyze usage patterns and trends to improve our website and services.
        - To protect our website, services, and users from fraudulent, unauthorized, or illegal activity.
        - To enforce our terms of service and other legal rights.
      `
    },
    {
      id: 'information-sharing',
      title: 'Information Sharing and Disclosure',
      icon: <FiGlobe />,
      content: `
        We may share your information in the following situations:
        
        - With service providers who perform services on our behalf, such as payment processing, data analysis, email delivery, 
          hosting services, and customer service.
        
        - With business partners with whom we jointly offer products or services, or whose products or services we believe may 
          interest you (only with your consent).
        
        - In connection with a business transaction such as a merger, acquisition, or sale of assets.
        
        - When we believe disclosure is necessary to protect our rights, protect your safety or the safety of others, investigate 
          fraud, or respond to a government request.
        
        - With your consent or at your direction.
        
        We do not sell your personal information to third parties.
      `
    },
    {
      id: 'data-security',
      title: 'Data Security',
      icon: <FiLock />,
      content: `
        We implement appropriate technical and organizational measures to protect the security of your personal information. 
        However, please be aware that no method of transmission over the Internet or method of electronic storage is 100% secure.
        
        We use industry-standard encryption to protect your payment information during transmission. We also maintain administrative, 
        technical, and physical safeguards to protect against unauthorized access, use, modification, and disclosure of personal information.
        
        While we strive to use commercially acceptable means to protect your personal information, we cannot guarantee its absolute security.
      `
    },
    {
      id: 'cookies',
      title: 'Cookies and Tracking Technologies',
      icon: <FiCookie />,
      content: `
        We use cookies and similar tracking technologies to track activity on our website and hold certain information. Cookies are 
        files with a small amount of data which may include an anonymous unique identifier.
        
        Types of cookies we use:
        
        - Essential cookies: Necessary for the website to function properly.
        - Preference cookies: Enable the website to remember your preferences and settings.
        - Analytics cookies: Help us understand how visitors interact with our website.
        - Marketing cookies: Used to track visitors across websites to display relevant advertisements.
        
        You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent. However, if you do not accept 
        cookies, you may not be able to use some portions of our website.
      `
    },
    {
      id: 'user-rights',
      title: 'Your Rights and Choices',
      icon: <FiShield />,
      content: `
        Depending on your location, you may have certain rights regarding your personal information, including:
        
        - Access: You can request access to your personal information we hold.
        - Correction: You can request correction of your personal information if it is inaccurate or incomplete.
        - Deletion: You can request deletion of your personal information in certain circumstances.
        - Restriction: You can request restriction of processing of your personal information.
        - Data portability: You can request transfer of your personal information to you or a third party.
        - Objection: You can object to processing of your personal information in certain circumstances.
        
        To exercise these rights, please contact us using the information provided at the end of this policy.
        
        For European Economic Area (EEA) residents, we process your personal information in compliance with the General Data Protection 
        Regulation (GDPR). For California residents, we comply with the California Consumer Privacy Act (CCPA).
      `
    },
    {
      id: 'children',
      title: 'Children\'s Privacy',
      icon: <FiAlertCircle />,
      content: `
        Our website is not intended for children under 16 years of age. We do not knowingly collect personal information from children 
        under 16. If you are a parent or guardian and you are aware that your child has provided us with personal information, please 
        contact us. If we become aware that we have collected personal information from children without verification of parental consent, 
        we take steps to remove that information from our servers.
      `
    },
    {
      id: 'changes',
      title: 'Changes to This Privacy Policy',
      icon: <FiDatabase />,
      content: `
        We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page 
        and updating the "Last updated" date.
        
        You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they 
        are posted on this page.
      `
    }
  ];
  
  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <PageTransition>
        <main className="flex-grow pt-24 pb-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              {/* Page Title */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="text-center mb-12"
              >
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Privacy Policy</h1>
                <p className="text-gray-600 dark:text-gray-300">
                  Last updated: {lastUpdated}
                </p>
              </motion.div>
              
              {/* Introduction */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8"
              >
                <p className="text-gray-700 dark:text-gray-300">
                  At 3DSKETCHUP.NET, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, 
                  and safeguard your information when you visit our website or use our services. Please read this policy carefully. 
                  If you do not agree with the terms of this privacy policy, please do not access the site.
                </p>
              </motion.div>
              
              {/* Table of Contents */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8"
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Table of Contents</h2>
                <ul className="space-y-2">
                  {sections.map((section, index) => (
                    <li key={index}>
                      <a 
                        href={`#${section.id}`} 
                        className="flex items-center text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        <span className="mr-2">{section.icon}</span>
                        <span>{section.title}</span>
                      </a>
                    </li>
                  ))}
                </ul>
              </motion.div>
              
              {/* Privacy Policy Sections */}
              {sections.map((section, index) => (
                <motion.div 
                  key={section.id}
                  id={section.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 + (index * 0.1) }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 mb-8"
                >
                  <div className="flex items-center mb-4">
                    <div className="text-blue-600 dark:text-blue-400 mr-3">
                      {section.icon}
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{section.title}</h2>
                  </div>
                  <div className="text-gray-700 dark:text-gray-300 whitespace-pre-line">
                    {section.content}
                  </div>
                </motion.div>
              ))}
              
              {/* Contact Information */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6"
              >
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Contact Us</h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  If you have any questions or concerns about this Privacy Policy, please contact us at:
                </p>
                <div className="text-gray-700 dark:text-gray-300">
                  <p>Email: <EMAIL></p>
                  <p>Address: 123 Design Street, Suite 456, San Francisco, CA 94107, United States</p>
                </div>
              </motion.div>
            </div>
          </div>
        </main>
      </PageTransition>
      
      <Footer />
    </div>
  );
};

export default Privacy;
