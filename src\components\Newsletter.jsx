import React, { useState } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import { FiMail, FiCheck, FiAlertCircle, FiLoader } from 'react-icons/fi';

// Convert to functional component to fix hook issues
const Newsletter = () => {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState('idle'); // idle, loading, success, error
  const [message, setMessage] = useState('');
  const [isValidEmail, setIsValidEmail] = useState(true);

  // Validate email format
  const validateEmail = (email) => {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  };

  const handleEmailChange = (e) => {
    const newEmail = e.target.value;

    // Only validate if there's some input (don't show error when field is empty)
    if (newEmail) {
      setEmail(newEmail);
      setIsValidEmail(validateEmail(newEmail));
    } else {
      setEmail(newEmail);
      setIsValidEmail(true);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Final validation before submission
    if (!email || !validateEmail(email)) {
      setIsValidEmail(false);
      return;
    }

    setStatus('loading');
    setMessage('');

    try {
      // Call the backend API to subscribe
      const response = await axios.post('http://localhost:5002/api/newsletter/subscribe', {
        email,
        preferences: {
          newModels: true,
          promotions: true,
          blogPosts: true,
          tutorials: true
        },
        source: 'website'
      });

      if (response.data.success) {
        setStatus('success');
        setMessage(response.data.message || 'Thank you for subscribing! You will now receive our latest updates.');
        setEmail('');
      } else {
        throw new Error(response.data.message || 'Subscription failed');
      }
    } catch (error) {
      setStatus('error');
      setMessage(
        error.response?.data?.message ||
        error.message ||
        'Something went wrong. Please try again later.'
      );
    }
  };

  return (
    <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-6 sm:p-8 shadow-xl">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold mb-2 text-white">Subscribe to Our Newsletter</h3>
        <p className="text-blue-100">Get the latest 3D models and updates delivered to your inbox</p>
      </div>

      <form onSubmit={handleSubmit} className="max-w-xl mx-auto">
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiMail className="h-5 w-5 text-gray-500" />
            </div>
            <input
              type="email"
              value={email}
              onChange={handleEmailChange}
              placeholder="Your email address"
              className={`w-full pl-10 pr-4 py-3 rounded-lg focus:outline-none focus:ring-2 ${
                isValidEmail
                  ? 'focus:ring-white text-gray-900 border-transparent'
                  : 'focus:ring-red-500 border-red-500 text-red-900 bg-red-50'
              }`}
              disabled={status === 'loading' || status === 'success'}
              required
            />
            {!isValidEmail && (
              <p className="mt-1 text-sm text-red-200">Please enter a valid email address</p>
            )}
          </div>
          <button
            type="submit"
            disabled={status === 'loading' || status === 'success'}
            className={`w-full sm:w-auto px-6 py-3 rounded-lg font-medium transition-colors ${
              status === 'success'
                ? 'bg-green-500 text-white cursor-default'
                : status === 'loading'
                ? 'bg-gray-300 text-gray-700 cursor-wait'
                : 'bg-white text-blue-600 hover:bg-blue-50'
            }`}
          >
            {status === 'loading' ? (
              <span className="flex items-center">
                <FiLoader className="animate-spin mr-2" />
                Subscribing...
              </span>
            ) : status === 'success' ? (
              <span className="flex items-center">
                <FiCheck className="mr-2" />
                Subscribed!
              </span>
            ) : (
              'Subscribe'
            )}
          </button>
        </div>

        {/* Status message */}
        {message && (
          <div className={`mt-4 p-3 rounded-lg ${
            status === 'success'
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-start">
              {status === 'success' ? (
                <FiCheck className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              ) : (
                <FiAlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              )}
              <p>{message}</p>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default Newsletter;
