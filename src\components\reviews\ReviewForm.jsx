import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { FiStar, FiSend } from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import Button from '../ui/Button';
import Alert from '../ui/Alert';

const ReviewForm = ({ modelId, onReviewSubmitted }) => {
  const { currentUser, isAuthenticated } = useAuth();
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [title, setTitle] = useState('');
  const [comment, setComment] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Character limits
  const TITLE_LIMIT = 100;
  const COMMENT_LIMIT = 1000;

  // Handle star hover
  const handleStarHover = (hoveredRating) => {
    setHoverRating(hoveredRating);
  };

  // Handle star click
  const handleStarClick = (selectedRating) => {
    setRating(selectedRating);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (rating === 0) {
      setError('Please select a rating');
      return;
    }
    
    if (!comment.trim()) {
      setError('Please enter a comment');
      return;
    }
    
    if (comment.length > COMMENT_LIMIT) {
      setError(`Comment must be ${COMMENT_LIMIT} characters or less`);
      return;
    }
    
    if (title.length > TITLE_LIMIT) {
      setError(`Title must be ${TITLE_LIMIT} characters or less`);
      return;
    }
    
    setError('');
    setIsSubmitting(true);
    
    try {
      const response = await fetch('http://localhost:5001/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          model: modelId,
          rating,
          title: title.trim() || undefined, // Only send if not empty
          comment: comment.trim()
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit review');
      }
      
      // Reset form
      setRating(0);
      setTitle('');
      setComment('');
      setSuccess('Your review has been submitted successfully!');
      
      // Notify parent component
      if (onReviewSubmitted) {
        onReviewSubmitted(data.data);
      }
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Render stars
  const renderStars = () => {
    return [1, 2, 3, 4, 5].map((star) => (
      <button
        key={star}
        type="button"
        className={`text-2xl focus:outline-none transition-colors ${
          (hoverRating || rating) >= star
            ? 'text-yellow-400'
            : 'text-gray-300 dark:text-gray-600'
        }`}
        onMouseEnter={() => handleStarHover(star)}
        onMouseLeave={() => handleStarHover(0)}
        onClick={() => handleStarClick(star)}
        aria-label={`Rate ${star} stars`}
      >
        <FiStar className="h-8 w-8 fill-current" />
      </button>
    ));
  };
  
  if (!isAuthenticated) {
    return (
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-medium mb-4">Write a Review</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Please sign in to leave a review.
        </p>
        <Button
          to="/login"
          variant="primary"
          size="md"
        >
          Sign In
        </Button>
      </div>
    );
  }
  
  return (
    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6">
      <h3 className="text-lg font-medium mb-4">Write a Review</h3>
      
      {error && (
        <Alert
          variant="error"
          title="Error"
          dismissible
          onDismiss={() => setError('')}
          className="mb-4"
        >
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert
          variant="success"
          title="Success"
          dismissible
          onDismiss={() => setSuccess('')}
          className="mb-4"
        >
          {success}
        </Alert>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Rating
          </label>
          <div className="flex space-x-1">
            {renderStars()}
          </div>
        </div>
        
        <div className="mb-4">
          <label htmlFor="review-title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Title (Optional)
          </label>
          <input
            type="text"
            id="review-title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Summarize your experience"
            className="input w-full"
            maxLength={TITLE_LIMIT}
          />
          <div className="flex justify-end mt-1">
            <span className={`text-xs ${
              title.length > TITLE_LIMIT * 0.9 ? 'text-red-500' : 'text-gray-500'
            }`}>
              {title.length}/{TITLE_LIMIT}
            </span>
          </div>
        </div>
        
        <div className="mb-4">
          <label htmlFor="review-comment" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Comment
          </label>
          <textarea
            id="review-comment"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Share your thoughts about this model"
            className="input w-full h-32 resize-none"
            maxLength={COMMENT_LIMIT}
          />
          <div className="flex justify-end mt-1">
            <span className={`text-xs ${
              comment.length > COMMENT_LIMIT * 0.9 ? 'text-red-500' : 'text-gray-500'
            }`}>
              {comment.length}/{COMMENT_LIMIT}
            </span>
          </div>
        </div>
        
        <div className="flex justify-end">
          <Button
            type="submit"
            variant="primary"
            size="md"
            loading={isSubmitting}
            disabled={isSubmitting}
            rightIcon={<FiSend />}
          >
            Submit Review
          </Button>
        </div>
      </form>
    </div>
  );
};

ReviewForm.propTypes = {
  modelId: PropTypes.string.isRequired,
  onReviewSubmitted: PropTypes.func
};

export default ReviewForm;
