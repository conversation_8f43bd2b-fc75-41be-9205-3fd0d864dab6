import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import DarkModeToggle from './DarkModeToggle';
import { motion, AnimatePresence } from 'framer-motion';
import { FiMenu, FiX, FiUser, FiLogOut, FiLogIn, FiUserPlus, FiSearch, FiHeart, FiShoppingCart, FiDownload, FiUpload, FiCreditCard, FiGrid, FiZap, FiExternalLink, FiTool, FiCamera, FiSliders } from 'react-icons/fi';
import { FaRegLightbulb, FaRegBuilding, FaTree, FaCouch } from 'react-icons/fa';

// Import UI components
import Button from './ui/Button';
import Input from './ui/Input';
import Badge from './ui/Badge';
import ImageSearch from './search/ImageSearch';
import AdvancedSearch from './search/AdvancedSearch';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [showImageSearch, setShowImageSearch] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Categories with icons - fixed paths to match CategoryPage.jsx expectations and App.jsx routes
  const categories = [
    { name: 'Interior Scenes', href: '/category/interior', icon: <FaRegLightbulb className="mr-2" /> },
    { name: 'Exterior Scenes', href: '/category/exterior', icon: <FaRegBuilding className="mr-2" /> },
    { name: 'Landscape/Garden', href: '/category/landscape', icon: <FaTree className="mr-2" /> },
    { name: 'Models/Objects', href: '/category/models', icon: <FaCouch className="mr-2" /> },
    { name: 'Virtual Showrooms', href: '/showrooms', icon: <FiGrid className="mr-2" /> },
    { name: 'Smart Collections', href: '/collections', icon: <FiZap className="mr-2" /> },
    { name: '3D Warehouse', href: '/warehouse', icon: <FiExternalLink className="mr-2" /> },
    { name: 'Plugins & Extensions', href: '/plugins', icon: <FiTool className="mr-2" /> },
  ];

  // Check if header is scrolled
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Debug current user state (reduced logging)
  useEffect(() => {
    if (currentUser && !window.headerUserLogged) {
      console.log('Header component - User logged in:', currentUser.name);
      window.headerUserLogged = true;
    } else if (!currentUser && window.headerUserLogged) {
      console.log('Header component - User logged out');
      window.headerUserLogged = false;
    }
  }, [currentUser]);

  // Close menus when location changes
  useEffect(() => {
    setIsMenuOpen(false);
    setIsProfileMenuOpen(false);
    setIsSearchOpen(false);
  }, [location]);

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsProfileMenuOpen(false);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const handleAdvancedSearch = (searchParams) => {
    const queryParams = new URLSearchParams();

    Object.entries(searchParams).forEach(([key, value]) => {
      if (value && value !== '' && value !== 0) {
        queryParams.append(key, value);
      }
    });

    navigate(`/search?${queryParams.toString()}`);
    setShowAdvancedSearch(false);
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 w-full z-[10000] transition-all duration-300 ${
        isScrolled
          ? 'bg-white/98 dark:bg-gray-900/98 backdrop-blur-md shadow-lg border-b border-gray-200/20 dark:border-gray-700/20 py-2'
          : 'bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm py-3'
      }`}
      style={{ margin: 0, padding: 0, minHeight: '64px' }}
    >
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 w-full">
          {/* Logo Section - Left */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center flex-shrink-0"
          >
            <Link to="/" className="flex items-center space-x-3 group">
              {/* Enhanced Animated Logo */}
              <div className="relative w-12 h-12 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-logo-glow transition-all duration-300 group-hover:scale-110 overflow-hidden animate-gradient-shift">
                {/* Animated background pulse */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500 animate-pulse"></div>

                {/* Rotating border effect */}
                <div className="absolute inset-0 rounded-xl border-2 border-transparent bg-gradient-to-r from-blue-300 via-purple-300 to-indigo-300 opacity-0 group-hover:opacity-50 animate-rotate-slow"></div>

                {/* Shimmer effect */}
                <div className="absolute inset-0 rounded-xl animate-shimmer opacity-30"></div>

                {/* Main logo content */}
                <div className="relative z-10 flex items-center justify-center">
                  <span className="text-white font-bold text-xl group-hover:text-yellow-200 transition-colors duration-300 animate-bounce-subtle drop-shadow-lg">3D</span>
                </div>

                {/* Enhanced floating particles */}
                <div className="absolute top-1 left-1 w-1.5 h-1.5 bg-white rounded-full opacity-60 animate-particle-float"></div>
                <div className="absolute top-2 right-2 w-1 h-1 bg-yellow-200 rounded-full opacity-80 animate-particle-float" style={{animationDelay: '0.5s'}}></div>
                <div className="absolute bottom-2 left-2 w-1 h-1 bg-blue-200 rounded-full opacity-70 animate-particle-float" style={{animationDelay: '1s'}}></div>
                <div className="absolute bottom-1 right-1 w-0.5 h-0.5 bg-purple-200 rounded-full opacity-90 animate-particle-float" style={{animationDelay: '1.5s'}}></div>

                {/* Glow effect on hover */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm"></div>
              </div>

              <div className="flex flex-col">
                <span className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 dark:text-white font-heading group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors whitespace-nowrap leading-tight">
                  3DSKETCHUP.NET
                </span>
                <span className="hidden sm:block text-xs text-gray-500 dark:text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-300 transition-colors">
                  Premium 3D Models
                </span>
              </div>
            </Link>
          </motion.div>

          {/* Mobile menu button */}
          <div className="flex items-center justify-end gap-3 md:hidden">
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="p-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg focus:outline-none transition-colors"
              aria-label="Search"
            >
              <FiSearch className="h-5 w-5" />
            </button>

            <DarkModeToggle />

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg focus:outline-none transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? (
                <FiX className="h-6 w-6" />
              ) : (
                <FiMenu className="h-6 w-6" />
              )}
            </button>
          </div>

          {/* Desktop navigation - Center section */}
          <div className="hidden lg:flex items-center justify-center flex-1 mx-8">
            <nav className="flex items-center space-x-2">
              {categories.slice(0, 4).map((category) => (
                <Link
                  key={category.name}
                  to={category.href}
                  className="flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 text-sm font-medium whitespace-nowrap group"
                >
                  <span className="text-lg group-hover:scale-110 transition-transform duration-200">{category.icon}</span>
                  <span className="hidden xl:inline">{category.name}</span>
                </Link>
              ))}
            </nav>
          </div>

          {/* Right section - Search, Dark Mode, User */}
          <div className="hidden md:flex items-center justify-end space-x-2">
            {/* Search buttons */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                className="p-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg focus:outline-none transition-colors"
                aria-label="Search"
                title="Text Search"
              >
                <FiSearch className="h-4 w-4" />
              </button>
              <button
                onClick={() => setShowImageSearch(true)}
                className="p-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg focus:outline-none transition-colors"
                aria-label="Image Search"
                title="Visual Search"
              >
                <FiCamera className="h-4 w-4" />
              </button>
              <button
                onClick={() => setShowAdvancedSearch(true)}
                className="p-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg focus:outline-none transition-colors"
                aria-label="Advanced Search"
                title="Advanced Search"
              >
                <FiSliders className="h-4 w-4" />
              </button>
            </div>

            <DarkModeToggle />

            {/* User menu */}
            <div className="relative">
              {currentUser ? (
                <>
                  <button
                    onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                    className="flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 focus:outline-none"
                  >
                    <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-2">
                      {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : 'U'}
                    </div>
                    <span className="hidden lg:inline font-medium whitespace-nowrap">{currentUser?.name || 'User'}</span>
                  </button>

                  <AnimatePresence>
                    {isProfileMenuOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-xl py-2 z-[10001] border border-gray-200 dark:border-gray-700"
                      >
                        <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">{currentUser?.name || 'User'}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{currentUser?.email || 'No email available'}</p>
                        </div>

                        <Link
                          to="/profile"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiUser className="h-4 w-4 mr-2" />
                          Your Profile
                        </Link>
                        <Link
                          to="/saved-models"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiHeart className="h-4 w-4 mr-2" />
                          Saved Models
                        </Link>
                        <Link
                          to="/downloads"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiDownload className="h-4 w-4 mr-2" />
                          Downloads
                        </Link>
                        <Link
                          to="/upload"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiUpload className="h-4 w-4 mr-2" />
                          Upload Model
                        </Link>
                        <Link
                          to="/subscription"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiCreditCard className="h-4 w-4 mr-2" />
                          Subscription
                        </Link>
                        {currentUser.role === 'admin' && (
                          <Link
                            to="/admin"
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                            onClick={() => setIsProfileMenuOpen(false)}
                          >
                            <FiShoppingCart className="h-4 w-4 mr-2" />
                            Admin Dashboard
                          </Link>
                        )}
                        <div className="border-t border-gray-200 dark:border-gray-700 mt-1 pt-1">
                          <button
                            onClick={handleLogout}
                            className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
                          >
                            <FiLogOut className="h-4 w-4 mr-2" />
                            Sign out
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </>
              ) : (
                <div className="flex space-x-2 whitespace-nowrap">
                  <Button
                    to="/login"
                    variant="secondary"
                    size="sm"
                    leftIcon={<FiLogIn className="h-4 w-4" />}
                  >
                    Login
                  </Button>
                  <Button
                    to="/register"
                    variant="primary"
                    size="sm"
                    leftIcon={<FiUserPlus className="h-4 w-4" />}
                  >
                    Register
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Search overlay */}
        <AnimatePresence>
          {isSearchOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 shadow-xl p-4 border-t border-gray-200 dark:border-gray-700 z-[10001]"
            >
              <form onSubmit={handleSearch} className="flex gap-2">
                <Input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search for models, scenes, objects..."
                  className="flex-1"
                  rightIcon={<FiSearch className="h-4 w-4 text-gray-400" />}
                  autoFocus
                />
                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                >
                  Search
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  size="md"
                  onClick={() => setIsSearchOpen(false)}
                >
                  Cancel
                </Button>
              </form>
              <div className="mt-3 flex flex-wrap gap-2 items-center">
                <span className="text-xs text-gray-500 dark:text-gray-400">Popular:</span>
                <Badge
                  variant="primary"
                  pill
                  onClick={() => {
                    setSearchQuery('modern interior');
                    handleSearch({ preventDefault: () => {} });
                  }}
                  className="cursor-pointer transition-colors"
                >
                  Modern Interior
                </Badge>
                <Badge
                  variant="accent"
                  pill
                  onClick={() => {
                    setSearchQuery('kitchen');
                    handleSearch({ preventDefault: () => {} });
                  }}
                  className="cursor-pointer transition-colors"
                >
                  Kitchen
                </Badge>
                <Badge
                  variant="secondary"
                  pill
                  onClick={() => {
                    setSearchQuery('office');
                    handleSearch({ preventDefault: () => {} });
                  }}
                  className="cursor-pointer transition-colors"
                >
                  Office
                </Badge>
                <div className="flex space-x-2 ml-auto">
                  <button
                    onClick={() => setShowImageSearch(true)}
                    className="flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full text-xs hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  >
                    <FiCamera className="h-3 w-3" />
                    <span>Visual Search</span>
                  </button>
                  <button
                    onClick={() => setShowAdvancedSearch(true)}
                    className="flex items-center space-x-1 px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    <FiSliders className="h-3 w-3" />
                    <span>Advanced</span>
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Mobile navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.nav
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-4 md:hidden bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 border border-gray-200 dark:border-gray-700 z-[10001]"
            >
              <div className="flex flex-col space-y-3">
                {categories.map((category) => (
                  <Link
                    key={category.name}
                    to={category.href}
                    className="flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors py-2"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {category.icon}
                    {category.name}
                  </Link>
                ))}

                <div className="pt-4 mt-2 border-t border-gray-200 dark:border-gray-700">
                  {currentUser ? (
                    <>
                      <Link
                        to="/profile"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiUser className="h-5 w-5 mr-2" />
                        Your Profile
                      </Link>
                      <Link
                        to="/saved-models"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiHeart className="h-5 w-5 mr-2" />
                        Saved Models
                      </Link>
                      <Link
                        to="/downloads"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiDownload className="h-5 w-5 mr-2" />
                        Downloads
                      </Link>
                      <Link
                        to="/upload"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiUpload className="h-5 w-5 mr-2" />
                        Upload Model
                      </Link>
                      <Link
                        to="/subscription"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiCreditCard className="h-5 w-5 mr-2" />
                        Subscription
                      </Link>
                      {currentUser.role === 'admin' && (
                        <Link
                          to="/admin"
                          className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <FiShoppingCart className="h-5 w-5 mr-2" />
                          Admin Dashboard
                        </Link>
                      )}
                      <button
                        onClick={() => {
                          handleLogout();
                          setIsMenuOpen(false);
                        }}
                        className="flex items-center py-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 w-full"
                      >
                        <FiLogOut className="h-5 w-5 mr-2" />
                        Sign out
                      </button>
                    </>
                  ) : (
                    <div className="flex flex-col space-y-3 pt-2">
                      <Button
                        to="/login"
                        variant="secondary"
                        fullWidth
                        leftIcon={<FiLogIn className="h-5 w-5" />}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Login
                      </Button>
                      <Button
                        to="/register"
                        variant="primary"
                        fullWidth
                        leftIcon={<FiUserPlus className="h-5 w-5" />}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Register
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </motion.nav>
          )}
        </AnimatePresence>
      </div>

      {/* Image Search Modal */}
      <ImageSearch
        isOpen={showImageSearch}
        onClose={() => setShowImageSearch(false)}
      />

      {/* Advanced Search Modal */}
      <AdvancedSearch
        isOpen={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onSearch={handleAdvancedSearch}
      />
    </header>
  );
};

export default Header;
