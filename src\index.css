@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');
@import url('https://rsms.me/inter/inter.css'); /* Import Inter var */
@import './styles/animations.css';
@import './styles/components.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Enhanced Primary color - Modern Blue */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;

  /* Secondary color - Slate */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;
  --secondary-950: #020617;

  /* Enhanced Accent color - Modern Teal */
  --accent-50: #f0fdfa;
  --accent-100: #ccfbf1;
  --accent-200: #99f6e4;
  --accent-300: #5eead4;
  --accent-400: #2dd4bf;
  --accent-500: #14b8a6;
  --accent-600: #0d9488;
  --accent-700: #0f766e;
  --accent-800: #115e59;
  --accent-900: #134e4a;
  --accent-950: #042f2e;

  /* New Gradient Colors */
  --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--accent-500) 0%, var(--primary-600) 100%);
  --gradient-hero: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 50%, var(--accent-600) 100%);
  --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

  /* Enhanced Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-glow-accent: 0 0 20px rgba(20, 184, 166, 0.3);

  /* Background colors */
  --background-light: #f9fafb;
  --background-dark: #0f172a;

  /* Text colors */
  --text-light: #1e293b;
  --text-dark: #f1f5f9;

  /* System properties */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

body {
  min-width: 320px;
  min-height: 100vh;
  font-family: 'Inter', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ensure header doesn't cause horizontal scroll */
header {
  max-width: 100vw;
  overflow-x: hidden;
  will-change: transform;
  backface-visibility: hidden;
}

/* Ensure proper stacking context for header elements */
.header-dropdown {
  position: relative;
  z-index: 10001;
}

/* Prevent layout shift when header changes */
.header-offset {
  padding-top: 100px;
}

@media (max-width: 768px) {
  .header-offset {
    padding-top: 90px;
  }
}

/* Professional Website Styling */
* {
  box-sizing: border-box;
}

/* Header z-index fix */
header {
  z-index: 999999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
}

/* Main content spacing */
.main-content {
  position: relative;
  z-index: 1;
  margin-top: 88px;
  min-height: calc(100vh - 88px);
}

/* Professional gradients */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #667eea 100%);
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

/* Glass morphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-card {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Professional shadows */
.shadow-professional {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
}

.shadow-professional-lg {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05);
}

/* Smooth transitions */
.transition-professional {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional hover effects */
.hover-lift-professional {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift-professional:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Custom utility classes are now in components.css */

/* Custom animations */
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dark mode styles */
.dark body {
  background-color: var(--background-dark);
  color: var(--text-dark);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-100);
}

.dark ::-webkit-scrollbar-track {
  background: var(--secondary-800);
}

::-webkit-scrollbar-thumb {
  background: var(--secondary-300);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--secondary-600);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-400);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-500);
}
