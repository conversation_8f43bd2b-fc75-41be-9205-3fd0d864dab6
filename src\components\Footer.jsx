import React from 'react';
import { Link } from 'react-router-dom';
import { FiFacebook, FiTwitter, FiInstagram, FiYoutube, FiMail, FiPhone, FiMapPin } from 'react-icons/fi';
import { motion } from 'framer-motion';
import Newsletter from './Newsletter';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Contact', href: '/contact' },
      { name: 'Careers', href: '/careers' },
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
    ],
    categories: [
      { name: 'Interior Scenes', href: '/category/interior' },
      { name: 'Exterior Scenes', href: '/category/exterior' },
      { name: 'Landscape/Garden', href: '/category/landscape' },
      { name: 'Models/Objects', href: '/category/models' },
      { name: 'All Categories', href: '/categories' },
    ],
    support: [
      { name: 'Help Center', href: '/help' },
      { name: 'FAQ', href: '/faq' },
      { name: 'Tutorials', href: '/tutorials' },
      { name: 'Pricing', href: '/pricing' },
      { name: 'Report an Issue', href: '/report' },
    ],
  };

  const socialLinks = [
    { name: 'Facebook', icon: <FiFacebook />, href: 'https://facebook.com' },
    { name: 'Twitter', icon: <FiTwitter />, href: 'https://twitter.com' },
    { name: 'Instagram', icon: <FiInstagram />, href: 'https://instagram.com' },
    { name: 'YouTube', icon: <FiYoutube />, href: 'https://youtube.com' },
  ];

  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        {/* Newsletter Section */}
        <div className="max-w-4xl mx-auto mb-16">
          <Newsletter />
        </div>

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          {/* Brand Column */}
          <div className="lg:col-span-2">
            <Link to="/" className="inline-block">
              <h2 className="text-2xl font-bold text-white mb-4 font-heading">3DSKETCHUP.NET</h2>
            </Link>
            <p className="text-gray-400 mb-6 max-w-md">
              Your premium source for high-quality 3D models, scenes, and assets for architectural visualization and design. Trusted by professionals worldwide.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((link) => (
                <a
                  key={link.name}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-gray-800 hover:bg-blue-600 text-white p-2 rounded-full transition-colors"
                  aria-label={link.name}
                >
                  {link.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Links Columns */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Company</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link to={link.href} className="text-gray-400 hover:text-white transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Categories</h3>
            <ul className="space-y-3">
              {footerLinks.categories.map((link) => (
                <li key={link.name}>
                  <Link to={link.href} className="text-gray-400 hover:text-white transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">Support</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link to={link.href} className="text-gray-400 hover:text-white transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>

            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-4 text-white">Contact</h3>
              <ul className="space-y-3 text-gray-400">
                <li className="flex items-center">
                  <FiMail className="mr-2" />
                  <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </li>
                <li className="flex items-center">
                  <FiPhone className="mr-2" />
                  <a href="tel:+1234567890" className="hover:text-white transition-colors">
                    +1 (234) 567-890
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 pt-8 mt-8 text-center text-gray-500 text-sm">
          <p>&copy; {currentYear} 3DSKETCHUP.NET. All rights reserved.</p>
          <p className="mt-2">
            <Link to="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link>
            {' • '}
            <Link to="/terms" className="hover:text-white transition-colors">Terms of Service</Link>
            {' • '}
            <Link to="/cookies" className="hover:text-white transition-colors">Cookie Policy</Link>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
