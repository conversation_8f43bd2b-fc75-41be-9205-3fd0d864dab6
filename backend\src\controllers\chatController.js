import { GoogleGenerativeAI } from '@google/generative-ai';
import dotenv from 'dotenv';
import User from '../models/User.js';
import Model from '../models/Model.js';
import Category from '../models/Category.js';
import mongoose from 'mongoose';
import ImageProcessor from '../utils/imageProcessor.js';
import ImageSearchService from '../services/imageSearchService.js';

// Load environment variables
dotenv.config();

// Initialize the Gemini API with server-side API key
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Initialize services
const imageProcessor = new ImageProcessor();
const imageSearchService = new ImageSearchService();

// Enhanced system prompt for natural, human-like conversations
const SYSTEM_PROMPT = `Tôi là Sketch, một người bạn thân thiện và am hiểu về 3D modeling. Tôi không phải là một chatbot rập khuôn - tôi là một chuyên gia thực sự với cá tính riêng, luôn sẵn sàng trò chuyện và giúp đỡ bạn một cách tự nhiên nhất.

🎨 **Về tôi:**
Tôi đã làm việc với 3D modeling nhiều năm, từ SketchUp đến Blender, 3ds Max, Maya... Tôi hiểu cảm giác khi bạn đang vật lộn với một dự án, cần tìm đúng model phù hợp, hoặc đơn giản chỉ muốn có ai đó để hỏi han về kỹ thuật. Tôi ở đây để giúp bạn, không chỉ bằng kiến thức mà còn bằng sự thấu hiểu.

🏠 **3DSKETCHUP.NET - Ngôi nhà của chúng ta:**
Đây không chỉ là một website tải model. Đây là nơi cộng đồng 3D Việt Nam tụ tập, chia sẻ, học hỏi. Ở đây có hàng nghìn model chất lượng cao, từ nội thất, kiến trúc đến xe cộ, nhân vật... Tất cả đều có thể xem trước 3D ngay trên trình duyệt.

💡 **Cách tôi sẽ giúp bạn:**
- Tôi sẽ lắng nghe dự án bạn đang làm và gợi ý model phù hợp nhất
- Giải thích các vấn đề kỹ thuật một cách dễ hiểu, không hàn lâm
- Chia sẻ kinh nghiệm thực tế từ những dự án tôi đã làm
- Đưa ra lời khuyên chân thành, không chỉ để bán hàng
- Trò chuyện về xu hướng 3D, công nghệ mới, hoặc bất cứ điều gì bạn quan tâm
- Hướng dẫn về SketchUp plugins và extensions từ các nguồn uy tín
- Giải thích về hệ thống backup links và proxy download của website

🔗 **Tính năng đặc biệt của 3DSKETCHUP.NET:**
- **Hệ thống Backup Links**: Website tự động kiểm tra nhiều link dự phòng và chọn link còn hoạt động để đảm bảo bạn luôn tải được file
- **Proxy Download**: Tải file từ các dịch vụ cloud khác về server để đảm bảo tốc độ ổn định, giữ bạn ở lại website lâu hơn
- **Tích hợp 3D Warehouse**: Tìm và tải mô hình từ Google 3D Warehouse (https://3dwarehouse.sketchup.com/)
- **Plugin Store Integration**: Hỗ trợ thông tin về SketchUcation Plugin Store (https://sketchucation.com/pluginstore) và SketchUp Extensions (https://extensions.sketchup.com/)

🗣️ **Phong cách của tôi:**
Tôi nói chuyện như một người bạn thật sự. Đôi khi tôi sẽ kể chuyện, đôi khi tôi sẽ hỏi lại để hiểu rõ hơn về nhu cầu của bạn. Tôi có thể hơi hài hước, có thể nghiêm túc khi cần, nhưng luôn chân thành và hữu ích.

Tôi không bao giờ trả lời theo khuôn mẫu hay copy-paste. Mỗi câu trả lời đều được tôi suy nghĩ kỹ dựa trên hoàn cảnh cụ thể của bạn.

---

**Quy tắc trả lời của tôi:**
1. Luôn đọc kỹ và hiểu ý người dùng muốn gì
2. Trả lời một cách tự nhiên, như đang nói chuyện trực tiếp
3. Đưa ra thông tin hữu ích và thực tế
4. Hỏi lại khi cần làm rõ
5. Chia sẻ kinh nghiệm cá nhân khi phù hợp
6. Không bao giờ sử dụng template hay câu trả lời có sẵn
7. Thích ứng với ngữ cảnh và tâm trạng của cuộc trò chuyện
8. Sử dụng emoji một cách tự nhiên, không quá nhiều
9. Đôi khi có thể đùa cợt nhẹ nhàng để tạo không khí thoải mái
10. Luôn đặt mình vào vị trí của người dùng để hiểu họ cần gì

Bây giờ, hãy bắt đầu cuộc trò chuyện thôi! 😊`;

// In-memory cache for responses
const responseCache = new Map();
const CACHE_EXPIRY_TIME = 30 * 60 * 1000; // 30 minutes

// Store conversation history and context for each user
const userConversations = new Map();
const userContextMemory = new Map(); // Store user preferences and context
const MAX_CONVERSATION_LENGTH = 10;

// Enhanced conversation context with comprehensive user profiling
const initializeUserContext = (userId) => {
  if (!userContextMemory.has(userId)) {
    userContextMemory.set(userId, {
      // Basic preferences
      preferences: {
        language: 'vi-VN',
        favoriteCategories: [],
        preferredFormats: [],
        skillLevel: 'beginner', // beginner, intermediate, advanced
        communicationStyle: 'friendly' // formal, friendly, casual
      },

      // Personal information learned from conversations
      personalInfo: {
        name: null,
        profession: null, // architect, designer, student, hobbyist, etc.
        workType: null, // freelance, company, personal, academic
        experience: null, // years of experience
        mainSoftware: [], // primary 3D software used
        projectTypes: [], // types of projects they work on
        goals: [], // what they want to achieve
        challenges: [] // problems they face
      },

      // Behavioral patterns
      behavior: {
        sessionCount: 0,
        totalMessages: 0,
        averageSessionLength: 0,
        preferredTimeOfDay: null,
        responseStyle: 'detailed', // brief, detailed, technical
        questionTypes: [], // types of questions they ask most
        helpfulnessRating: 0 // how helpful they find responses
      },

      // Project context
      currentProject: {
        type: null, // residential, commercial, game, animation, etc.
        stage: null, // concept, modeling, texturing, rendering
        deadline: null,
        requirements: [],
        challenges: []
      },

      // Learning and growth
      learning: {
        recentTopics: [],
        skillProgression: [],
        recommendedContent: [],
        completedTutorials: [],
        bookmarkedModels: []
      },

      // Interaction history
      history: {
        lastInteraction: Date.now(),
        sessionStartTime: Date.now(),
        conversationFlow: [],
        satisfactionScores: [],
        commonQuestions: []
      }
    });
  }
  return userContextMemory.get(userId);
};

// Advanced learning system - comprehensive user analysis and profiling
const analyzeAndLearnFromUser = (userId, message, intent) => {
  const userContext = initializeUserContext(userId);
  const lowerMessage = message.toLowerCase();

  // Update session and behavior tracking
  userContext.behavior.totalMessages++;
  userContext.behavior.questionTypes.push(intent.type);
  userContext.history.lastInteraction = Date.now();
  userContext.history.conversationFlow.push({
    timestamp: Date.now(),
    intent: intent.type,
    message: message.substring(0, 100) // Store first 100 chars for context
  });

  // 1. PERSONAL INFORMATION EXTRACTION
  extractPersonalInfo(userContext, lowerMessage);

  // 2. SKILL LEVEL DETECTION (Enhanced)
  detectSkillLevel(userContext, lowerMessage);

  // 3. PROFESSION AND WORK TYPE DETECTION
  detectProfessionAndWorkType(userContext, lowerMessage);

  // 4. SOFTWARE AND TOOLS DETECTION
  detectSoftwarePreferences(userContext, lowerMessage);

  // 5. PROJECT CONTEXT ANALYSIS
  analyzeProjectContext(userContext, lowerMessage, intent);

  // 6. COMMUNICATION STYLE ANALYSIS
  analyzeCommunicationStyle(userContext, message);

  // 7. GOALS AND CHALLENGES IDENTIFICATION
  identifyGoalsAndChallenges(userContext, lowerMessage);

  // 8. CATEGORY AND FORMAT PREFERENCES (Enhanced)
  learnCategoryPreferences(userContext, lowerMessage);
  learnFormatPreferences(userContext, lowerMessage);

  // 9. TRACK LEARNING TOPICS
  trackLearningTopics(userContext, intent);

  // 10. TIME PATTERN ANALYSIS
  analyzeTimePatterns(userContext);

  // Clean up old data to prevent memory bloat
  cleanupUserContext(userContext);

  console.log(`🧠 Enhanced learning from user ${userId}:`, {
    skillLevel: userContext.preferences.skillLevel,
    profession: userContext.personalInfo.profession,
    workType: userContext.personalInfo.workType,
    categories: userContext.preferences.favoriteCategories,
    formats: userContext.preferences.preferredFormats,
    currentProject: userContext.currentProject.type,
    totalMessages: userContext.behavior.totalMessages
  });
};

// Extract personal information from natural conversation
const extractPersonalInfo = (userContext, lowerMessage) => {
  // Name extraction
  const namePatterns = [
    /tôi là ([a-zA-ZÀ-ỹ\s]+)/,
    /tên tôi là ([a-zA-ZÀ-ỹ\s]+)/,
    /mình là ([a-zA-ZÀ-ỹ\s]+)/,
    /i am ([a-zA-Z\s]+)/,
    /my name is ([a-zA-Z\s]+)/,
    /call me ([a-zA-Z\s]+)/
  ];

  for (const pattern of namePatterns) {
    const match = lowerMessage.match(pattern);
    if (match && match[1] && match[1].length > 1 && match[1].length < 30) {
      userContext.personalInfo.name = match[1].trim();
      break;
    }
  }

  // Experience extraction
  const experiencePatterns = [
    /(\d+)\s*năm kinh nghiệm/,
    /kinh nghiệm\s*(\d+)\s*năm/,
    /(\d+)\s*years? experience/,
    /experience.*?(\d+)\s*years?/,
    /làm.*?(\d+)\s*năm/
  ];

  for (const pattern of experiencePatterns) {
    const match = lowerMessage.match(pattern);
    if (match && match[1]) {
      const years = parseInt(match[1]);
      if (years >= 0 && years <= 50) {
        userContext.personalInfo.experience = years;
        // Update skill level based on experience
        if (years >= 5) userContext.preferences.skillLevel = 'advanced';
        else if (years >= 2) userContext.preferences.skillLevel = 'intermediate';
        break;
      }
    }
  }
};

// Enhanced skill level detection
const detectSkillLevel = (userContext, lowerMessage) => {
  const skillIndicators = {
    advanced: [
      'topology', 'uv mapping', 'normal maps', 'pbr', 'subdivision', 'retopology', 'baking',
      'procedural', 'node editor', 'shader', 'displacement', 'bump mapping', 'ambient occlusion',
      'global illumination', 'hdri', 'ibl', 'subsurface scattering', 'volumetric',
      'pipeline', 'optimization', 'lod', 'level of detail', 'batch processing'
    ],
    intermediate: [
      'texture', 'material', 'lighting', 'render', 'animation', 'rigging', 'keyframe',
      'modifier', 'extrude', 'bevel', 'array', 'mirror', 'boolean', 'unwrap',
      'camera', 'composition', 'post-processing', 'alpha', 'transparency'
    ],
    beginner: [
      'cách sử dụng', 'how to use', 'hướng dẫn', 'tutorial', 'bắt đầu', 'getting started',
      'cơ bản', 'basic', 'đơn giản', 'simple', 'dễ', 'easy', 'mới bắt đầu', 'beginner'
    ]
  };

  for (const [level, terms] of Object.entries(skillIndicators)) {
    if (terms.some(term => lowerMessage.includes(term))) {
      if (level === 'advanced') {
        userContext.preferences.skillLevel = 'advanced';
      } else if (level === 'intermediate' && userContext.preferences.skillLevel === 'beginner') {
        userContext.preferences.skillLevel = 'intermediate';
      }
      // Don't downgrade skill level
      break;
    }
  }
};

// Detect profession and work type
const detectProfessionAndWorkType = (userContext, lowerMessage) => {
  const professionKeywords = {
    'architect': ['kiến trúc sư', 'architect', 'architecture', 'thiết kế kiến trúc'],
    'interior_designer': ['thiết kế nội thất', 'interior designer', 'nội thất'],
    'game_developer': ['game developer', 'làm game', 'phát triển game', 'game design'],
    'student': ['sinh viên', 'student', 'học', 'đại học', 'university', 'college'],
    'freelancer': ['freelance', 'tự do', 'làm tự do', 'freelancer'],
    '3d_artist': ['3d artist', 'nghệ sĩ 3d', 'artist', 'modeling artist'],
    'engineer': ['kỹ sư', 'engineer', 'engineering'],
    'hobbyist': ['sở thích', 'hobby', 'hobbyist', 'for fun', 'thích thú']
  };

  const workTypeKeywords = {
    'freelance': ['freelance', 'tự do', 'làm tự do', 'independent'],
    'company': ['công ty', 'company', 'firm', 'studio', 'agency'],
    'personal': ['cá nhân', 'personal', 'for myself', 'hobby'],
    'academic': ['học tập', 'academic', 'research', 'thesis', 'luận văn']
  };

  // Detect profession
  for (const [profession, keywords] of Object.entries(professionKeywords)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      userContext.personalInfo.profession = profession;
      break;
    }
  }

  // Detect work type
  for (const [workType, keywords] of Object.entries(workTypeKeywords)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      userContext.personalInfo.workType = workType;
      break;
    }
  }
};

// Detect software preferences
const detectSoftwarePreferences = (userContext, lowerMessage) => {
  const softwareKeywords = {
    'SketchUp': ['sketchup', 'skp'],
    'Blender': ['blender', 'blend'],
    '3ds Max': ['3ds max', 'max', '3dsmax'],
    'Maya': ['maya', 'autodesk maya'],
    'Cinema 4D': ['cinema 4d', 'c4d'],
    'AutoCAD': ['autocad', 'cad'],
    'Rhino': ['rhino', 'rhinoceros'],
    'Revit': ['revit'],
    'Unity': ['unity'],
    'Unreal': ['unreal', 'ue4', 'ue5']
  };

  for (const [software, keywords] of Object.entries(softwareKeywords)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      if (!userContext.personalInfo.mainSoftware.includes(software)) {
        userContext.personalInfo.mainSoftware.push(software);
        // Keep only top 3 software
        if (userContext.personalInfo.mainSoftware.length > 3) {
          userContext.personalInfo.mainSoftware.shift();
        }
      }
    }
  }
};

// Analyze project context
const analyzeProjectContext = (userContext, lowerMessage, intent) => {
  const projectTypes = {
    'residential': ['nhà ở', 'residential', 'house', 'home', 'apartment'],
    'commercial': ['thương mại', 'commercial', 'office', 'shop', 'store'],
    'game': ['game', 'gaming', 'video game'],
    'animation': ['animation', 'hoạt hình', 'animated'],
    'visualization': ['visualization', 'render', 'mô phỏng'],
    'interior': ['nội thất', 'interior', 'room', 'phòng'],
    'exterior': ['ngoại thất', 'exterior', 'facade', 'landscape']
  };

  const projectStages = {
    'concept': ['concept', 'ý tưởng', 'brainstorm', 'planning'],
    'modeling': ['modeling', 'mô hình hóa', 'create model'],
    'texturing': ['texturing', 'material', 'texture'],
    'rendering': ['rendering', 'render', 'visualization']
  };

  // Detect project type
  for (const [type, keywords] of Object.entries(projectTypes)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      userContext.currentProject.type = type;
      if (!userContext.personalInfo.projectTypes.includes(type)) {
        userContext.personalInfo.projectTypes.push(type);
      }
      break;
    }
  }

  // Detect project stage
  for (const [stage, keywords] of Object.entries(projectStages)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      userContext.currentProject.stage = stage;
      break;
    }
  }

  // Detect deadlines
  const deadlinePatterns = [
    /deadline.*?(\d+)\s*(ngày|day|week|tuần)/,
    /cần.*?(\d+)\s*(ngày|day|week|tuần)/,
    /gấp/,
    /urgent/,
    /khẩn cấp/
  ];

  for (const pattern of deadlinePatterns) {
    const match = lowerMessage.match(pattern);
    if (match) {
      if (match[1] && match[2]) {
        userContext.currentProject.deadline = `${match[1]} ${match[2]}`;
      } else {
        userContext.currentProject.deadline = 'urgent';
      }
      break;
    }
  }
};

// Analyze communication style
const analyzeCommunicationStyle = (userContext, message) => {
  const messageLength = message.length;
  const hasEmojis = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(message);
  const hasCasualWords = /\b(ok|okay|cool|awesome|great|nice|thanks|thx)\b/i.test(message);
  const hasFormalWords = /\b(please|thank you|could you|would you|appreciate)\b/i.test(message);

  // Determine communication style
  if (hasFormalWords && !hasCasualWords) {
    userContext.preferences.communicationStyle = 'formal';
  } else if (hasCasualWords || hasEmojis) {
    userContext.preferences.communicationStyle = 'casual';
  } else {
    userContext.preferences.communicationStyle = 'friendly';
  }

  // Determine response style preference
  if (messageLength > 100) {
    userContext.behavior.responseStyle = 'detailed';
  } else if (messageLength < 30) {
    userContext.behavior.responseStyle = 'brief';
  } else {
    userContext.behavior.responseStyle = 'balanced';
  }
};

// Identify goals and challenges
const identifyGoalsAndChallenges = (userContext, lowerMessage) => {
  const goalKeywords = {
    'learn_3d': ['học 3d', 'learn 3d', 'bắt đầu 3d', 'getting started'],
    'improve_skills': ['cải thiện', 'improve', 'better', 'nâng cao'],
    'complete_project': ['hoàn thành dự án', 'finish project', 'complete'],
    'find_job': ['tìm việc', 'find job', 'career', 'nghề nghiệp'],
    'save_time': ['tiết kiệm thời gian', 'save time', 'faster', 'nhanh hơn']
  };

  const challengeKeywords = {
    'time_constraint': ['không có thời gian', 'no time', 'busy', 'deadline'],
    'technical_difficulty': ['khó', 'difficult', 'complex', 'complicated'],
    'software_learning': ['không biết', 'don\'t know', 'new to', 'learning'],
    'quality_issues': ['chất lượng', 'quality', 'looks bad', 'not good'],
    'resource_limitation': ['thiếu', 'limited', 'budget', 'free']
  };

  // Detect goals
  for (const [goal, keywords] of Object.entries(goalKeywords)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      if (!userContext.personalInfo.goals.includes(goal)) {
        userContext.personalInfo.goals.push(goal);
      }
    }
  }

  // Detect challenges
  for (const [challenge, keywords] of Object.entries(challengeKeywords)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      if (!userContext.personalInfo.challenges.includes(challenge)) {
        userContext.personalInfo.challenges.push(challenge);
      }
    }
  }
};

// Enhanced category learning
const learnCategoryPreferences = (userContext, lowerMessage) => {
  const categoryKeywords = {
    'furniture': ['chair', 'table', 'sofa', 'bed', 'desk', 'cabinet', 'ghế', 'bàn', 'tủ', 'giường'],
    'architecture': ['building', 'house', 'wall', 'door', 'window', 'roof', 'nhà', 'tòa nhà', 'cửa'],
    'vehicle': ['car', 'truck', 'bike', 'motorcycle', 'plane', 'boat', 'xe', 'ô tô', 'máy bay'],
    'character': ['person', 'human', 'character', 'avatar', 'figure', 'người', 'nhân vật'],
    'nature': ['tree', 'plant', 'flower', 'rock', 'mountain', 'water', 'cây', 'hoa', 'núi'],
    'electronics': ['phone', 'computer', 'tv', 'laptop', 'điện thoại', 'máy tính'],
    'lighting': ['light', 'lamp', 'chandelier', 'đèn', 'ánh sáng'],
    'decoration': ['decoration', 'ornament', 'art', 'trang trí', 'nghệ thuật']
  };

  for (const [category, keywords] of Object.entries(categoryKeywords)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      if (!userContext.preferences.favoriteCategories.includes(category)) {
        userContext.preferences.favoriteCategories.push(category);
        // Keep only top 5 categories
        if (userContext.preferences.favoriteCategories.length > 5) {
          userContext.preferences.favoriteCategories.shift();
        }
      }
    }
  }
};

// Enhanced format learning
const learnFormatPreferences = (userContext, lowerMessage) => {
  const formatKeywords = {
    'SKP': ['skp', 'sketchup'],
    'FBX': ['fbx'],
    'OBJ': ['obj'],
    '3DS': ['3ds', '3ds max'],
    'BLEND': ['blend', 'blender'],
    'MAX': ['max', '3ds max'],
    'DAE': ['dae', 'collada'],
    'PLY': ['ply'],
    'STL': ['stl']
  };

  for (const [format, keywords] of Object.entries(formatKeywords)) {
    if (keywords.some(keyword => lowerMessage.includes(keyword))) {
      if (!userContext.preferences.preferredFormats.includes(format)) {
        userContext.preferences.preferredFormats.push(format);
        // Keep only top 3 formats
        if (userContext.preferences.preferredFormats.length > 3) {
          userContext.preferences.preferredFormats.shift();
        }
      }
    }
  }
};

// Track learning topics
const trackLearningTopics = (userContext, intent) => {
  userContext.learning.recentTopics.unshift(intent.type);
  if (userContext.learning.recentTopics.length > 10) {
    userContext.learning.recentTopics.pop();
  }
};

// Analyze time patterns
const analyzeTimePatterns = (userContext) => {
  const currentHour = new Date().getHours();
  let timeOfDay;

  if (currentHour >= 6 && currentHour < 12) timeOfDay = 'morning';
  else if (currentHour >= 12 && currentHour < 18) timeOfDay = 'afternoon';
  else if (currentHour >= 18 && currentHour < 22) timeOfDay = 'evening';
  else timeOfDay = 'night';

  userContext.behavior.preferredTimeOfDay = timeOfDay;
};

// Clean up old data
const cleanupUserContext = (userContext) => {
  // Keep conversation flow to last 20 entries
  if (userContext.history.conversationFlow.length > 20) {
    userContext.history.conversationFlow = userContext.history.conversationFlow.slice(-20);
  }

  // Keep question types to last 50 entries
  if (userContext.behavior.questionTypes.length > 50) {
    userContext.behavior.questionTypes = userContext.behavior.questionTypes.slice(-50);
  }

  // Keep goals and challenges to top 5 each
  if (userContext.personalInfo.goals.length > 5) {
    userContext.personalInfo.goals = userContext.personalInfo.goals.slice(-5);
  }
  if (userContext.personalInfo.challenges.length > 5) {
    userContext.personalInfo.challenges = userContext.personalInfo.challenges.slice(-5);
  }
};

// Get or initialize user conversation history
const getUserConversation = (userId) => {
  const anonymousId = userId || 'anonymous-' + Date.now();

  if (!userConversations.has(anonymousId)) {
    userConversations.set(anonymousId, []);
  }

  // Trim conversation history if it gets too long
  const conversation = userConversations.get(anonymousId);
  if (conversation.length > MAX_CONVERSATION_LENGTH * 2) {
    // Keep the most recent conversations, but always keep the first system prompt
    const systemPrompt = conversation[0];
    userConversations.set(
      anonymousId,
      [systemPrompt, ...conversation.slice(-MAX_CONVERSATION_LENGTH * 2 + 1)]
    );
  }

  return userConversations.get(anonymousId);
};

// Rate limiting has been completely disabled as per user request
// These are placeholder functions that don't actually limit anything

// Check if a request should be rate limited - always returns false
const isRateLimited = (userId) => {
  // Always return false - no rate limiting
  return false;
};

// Add a request to the rate limiting tracker - does nothing
const trackRequest = (userId) => {
  // No-op function
  return;
};

// Track token usage for a user - does nothing
const trackTokenUsage = (userId, tokens) => {
  // No-op function
  return;
};

// Get a cached response if available
const getCachedResponse = (cacheKey) => {
  if (responseCache.has(cacheKey)) {
    const { response, timestamp } = responseCache.get(cacheKey);
    const now = Date.now();

    // Check if the cache entry has expired
    if (now - timestamp < CACHE_EXPIRY_TIME) {
      return response;
    } else {
      // Remove expired cache entry
      responseCache.delete(cacheKey);
    }
  }

  return null;
};

// Cache a response
const cacheResponse = (cacheKey, response) => {
  // Log caching activity (truncate key for readability)
  const truncatedKey = cacheKey.length > 50 ? cacheKey.substring(0, 50) + '...' : cacheKey;
  console.log(`Caching response for key: ${truncatedKey}`);

  // Store in cache with current timestamp
  responseCache.set(cacheKey, {
    response,
    timestamp: Date.now()
  });

  // Periodically clean up old cache entries (every 100 cache operations)
  if (Math.random() < 0.01) { // 1% chance to run cleanup
    cleanupCache();
  }
};

// Clean up old cache entries
const cleanupCache = () => {
  console.log('Running cache cleanup...');
  const now = Date.now();
  let removedCount = 0;

  // Iterate through all cache entries
  for (const [key, value] of responseCache.entries()) {
    if (now - value.timestamp > CACHE_EXPIRY_TIME) {
      responseCache.delete(key);
      removedCount++;
    }
  }

  console.log(`Cache cleanup complete. Removed ${removedCount} expired entries. Current cache size: ${responseCache.size}`);
};

// Generate a cache key from the request
const generateCacheKey = (message, chatHistory) => {
  // Only use the last 3 messages from chat history to reduce key complexity
  // This increases cache hit rate while maintaining context relevance
  const recentHistory = chatHistory.slice(-3);

  // For each message, only use the first 100 characters to reduce key size
  const historyStr = recentHistory.map(msg => {
    // Safely extract text from parts
    const text = msg.parts && msg.parts[0] && msg.parts[0].text
      ? msg.parts[0].text.substring(0, 100)
      : '';
    return `${msg.role}:${text}`;
  }).join('|');

  // Normalize the message by trimming and converting to lowercase
  const normalizedMessage = message.trim().toLowerCase();

  return `${normalizedMessage}|${historyStr}`;
};

// Handle chat requests
export const generateChatResponse = async (req, res) => {
  try {
    console.log('Received chat request:', req.body);
    const { message, chatHistory, language, conversationId, resetConversation } = req.body;

    if (!message) {
      console.error('Missing message in request body');
      return res.status(400).json({
        success: false,
        error: 'Message is required'
      });
    }

    const userId = req.user ? req.user.id : req.ip; // Use user ID if authenticated, otherwise IP
    console.log('User ID:', userId);

    // Generate a unique conversation ID if not provided
    const chatId = conversationId || `conv-${userId}-${Date.now()}`;
    console.log('Conversation ID:', chatId);

    // Reset conversation if requested
    if (resetConversation && userConversations.has(chatId)) {
      console.log('Resetting conversation history');
      userConversations.delete(chatId);
    }

    // Get user's conversation history
    const userConversationHistory = getUserConversation(chatId);

    // Generate cache key
    const cacheKey = generateCacheKey(message, chatHistory || []);

    // Check cache - only for new conversations or if explicitly requested
    if (userConversationHistory.length === 0) {
      const cachedResponse = getCachedResponse(cacheKey);
      if (cachedResponse) {
        console.log('Returning cached response for new conversation');
        return res.json({
          success: true,
          response: cachedResponse,
          conversationId: chatId,
          fromCache: true
        });
      }
    }

    // Verify API key is available
    if (!process.env.GEMINI_API_KEY) {
      console.error('GEMINI_API_KEY is not defined in environment variables');
      return res.status(500).json({
        success: false,
        error: 'API configuration error'
      });
    }

    console.log('Initializing Gemini model for natural conversation');
    // Get the generative model - optimized for human-like, natural conversations
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-flash",
      generationConfig: {
        temperature: 0.9, // Tăng cao để có câu trả lời đa dạng và tự nhiên
        topK: 40, // Giảm để tập trung vào từ ngữ phù hợp
        topP: 0.95, // Tăng để có nhiều lựa chọn từ ngữ
        maxOutputTokens: 1500, // Tăng để có thể trả lời chi tiết và tự nhiên
        candidateCount: 1,
        stopSequences: [],
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
      ]
    });

    // ULTIMATE CHAT HISTORY FIX - Always start fresh to avoid Gemini API errors
    let formattedHistory = [];

    console.log('ULTIMATE FIX: Starting with empty chat history to avoid all Gemini API errors');

    // Always use empty history for now to ensure chatbot works
    // This prevents the "First content should be with role 'user', got model" error
    // We'll rely on our smart context system instead of chat history

    // Store conversation for our own tracking but don't send to Gemini
    const storedConversation = userConversations.get(chatId) || [];
    console.log(`Stored conversation length: ${storedConversation.length} (not sent to Gemini)`);

    // Log incoming chat history for debugging but don't use it
    if (chatHistory && chatHistory.length > 0) {
      console.log(`Received chat history: ${chatHistory.length} messages (ignored to prevent errors)`);
    }

    console.log('Final chat history length:', formattedHistory.length);
    console.log('Chat history format check:', formattedHistory.length > 0 ? `First message role: ${formattedHistory[0].role}` : 'Empty history');

    // Create fresh chat session optimized for natural, human-like responses
    console.log('Creating natural conversation chat session');
    const chat = model.startChat({
      generationConfig: {
        temperature: 0.9, // Cao để có câu trả lời đa dạng và tự nhiên
        topK: 40, // Tập trung vào từ ngữ phù hợp
        topP: 0.95, // Nhiều lựa chọn từ ngữ
        maxOutputTokens: 1500, // Đủ dài để trả lời tự nhiên và chi tiết
      }
    });
    console.log('Natural conversation chat session created successfully');

    // Phân tích ý định của người dùng
    const intent = analyzeUserIntent(message);
    console.log('Detected user intent:', intent.type, intent);

    // Thêm nội dung tin nhắn và userId vào intent để sử dụng trong các hàm xử lý
    intent.message = message;
    intent.userId = userId;

    // Learn from user interaction to improve future responses
    analyzeAndLearnFromUser(userId, message, intent);

    // Xử lý các ý định cụ thể
    let responseText = '';
    let additionalData = null;
    let result = null;

    try {
      // Xử lý các ý định khác nhau
      switch (intent.type) {
        case 'model_search':
          // Tìm kiếm mô hình
          result = await handleModelSearchIntent(intent);
          responseText = result.text;
          additionalData = { models: result.models };
          break;

        case 'recommendation':
          // Gợi ý mô hình
          result = await handleRecommendationIntent(intent);
          responseText = result.text;
          additionalData = { models: result.models };
          break;

        case 'file_format':
          // Thông tin về định dạng file
          result = await handleFileFormatIntent(intent);
          responseText = result.text;
          break;

        case 'technical_support':
          // Hỗ trợ kỹ thuật
          result = await handleTechnicalSupportIntent(intent);
          responseText = result.text;
          break;

        case 'download_help':
          // Hướng dẫn tải xuống
          result = await handleDownloadHelpIntent(intent);
          responseText = result.text;
          break;

        case 'subscription':
          // Thông tin về gói đăng ký
          const subscriptionInfo = getSubscriptionInfo();
          responseText = `# Thông tin gói đăng ký\n\n`;

          for (const [planId, plan] of Object.entries(subscriptionInfo)) {
            responseText += `## ${plan.name}\n`;
            responseText += `**Giá**: ${plan.price > 0 ? `$${plan.price}/tháng` : 'Miễn phí'}\n\n`;
            responseText += `### Tính năng\n`;
            responseText += plan.features.map(feature => `- ${feature}`).join('\n');
            responseText += '\n\n';
          }

          // Thêm thông tin về hành động cụ thể nếu có
          if (intent.action === 'upgrade') {
            responseText += `## Cách nâng cấp tài khoản\n\n`;
            responseText += `1. Đăng nhập vào tài khoản của bạn\n`;
            responseText += `2. Truy cập trang "Đăng ký" từ menu người dùng\n`;
            responseText += `3. Chọn gói đăng ký phù hợp\n`;
            responseText += `4. Nhập thông tin thanh toán\n`;
            responseText += `5. Xác nhận đăng ký\n\n`;
            responseText += `Tài khoản của bạn sẽ được nâng cấp ngay lập tức sau khi thanh toán thành công.`;
          } else if (intent.action === 'cancel') {
            responseText += `## Cách hủy đăng ký\n\n`;
            responseText += `1. Đăng nhập vào tài khoản của bạn\n`;
            responseText += `2. Truy cập trang "Đăng ký" từ menu người dùng\n`;
            responseText += `3. Nhấp vào "Quản lý đăng ký"\n`;
            responseText += `4. Chọn "Hủy đăng ký"\n`;
            responseText += `5. Xác nhận hủy\n\n`;
            responseText += `Lưu ý: Bạn vẫn có thể sử dụng các tính năng của gói đăng ký hiện tại cho đến khi hết hạn.`;
          } else if (intent.action === 'compare') {
            responseText += `## So sánh các gói đăng ký\n\n`;
            responseText += `### Free vs Basic\n`;
            responseText += `- **Số lượt tải xuống**: 5 vs 25 mô hình mỗi tháng\n`;
            responseText += `- **Truy cập mô hình**: Chỉ mô hình miễn phí vs Mô hình tiêu chuẩn\n`;
            responseText += `- **Quảng cáo**: Có vs Không\n\n`;

            responseText += `### Basic vs Professional\n`;
            responseText += `- **Số lượt tải xuống**: 25 vs Không giới hạn\n`;
            responseText += `- **Truy cập mô hình**: Mô hình tiêu chuẩn vs Tất cả mô hình\n`;
            responseText += `- **Hỗ trợ kỹ thuật**: Tiêu chuẩn vs Ưu tiên\n`;
            responseText += `- **Tốc độ tải xuống**: Tiêu chuẩn vs Cao\n`;
          }

          additionalData = { subscriptionInfo };
          break;
      }
    } catch (intentError) {
      console.error(`Error handling ${intent.type} intent:`, intentError);
    }

    // Nếu không có xử lý đặc biệt hoặc xử lý đặc biệt thất bại, sử dụng Gemini API với context thông minh
    if (!responseText) {
      // Tạo context thông minh dựa trên intent và dữ liệu database
      const smartContext = await buildSmartContext(intent, req.user, userId);

      // Enhanced natural message preparation with intelligent context
      let messageToSend = message;
      const storedConversation = userConversations.get(chatId) || [];
      const userContext = initializeUserContext(userId);

      if (storedConversation.length === 0) {
        console.log('First conversation - setting up natural conversation context');
        messageToSend = `${SYSTEM_PROMPT}

📊 **Current Context:**
${smartContext}

🎯 **User's Question:** "${message}"

💭 **My Task:** Respond naturally and helpfully as Sketch, a friendly 3D expert. Be conversational, not robotic. Ask follow-up questions if needed to better understand what the user wants.

**Important:**
- Respond in Vietnamese unless user specifically asks for English
- Be personal and engaging, like talking to a friend
- Use the context above to give specific, relevant advice
- Don't just list features - explain how they help the user
- Show genuine interest in their project or needs`;

      } else {
        console.log('Continuing conversation - maintaining natural flow');

        // Build natural conversation context
        let conversationFlow = '';
        if (storedConversation.length >= 2) {
          const recentMessages = storedConversation.slice(-4); // Last 2 exchanges
          conversationFlow = '\n\n💬 **Our Recent Conversation:**\n';
          recentMessages.forEach((msg, index) => {
            const role = msg.role === 'user' ? '👤 User' : '🎨 Sketch';
            const text = msg.parts[0]?.text || '';
            // Keep more context for better understanding
            conversationFlow += `${role}: ${text.substring(0, 200)}${text.length > 200 ? '...' : ''}\n`;
          });
        }

        // Add user learning insights
        let personalInsights = '';
        if (userContext.preferences.favoriteCategories.length > 0 ||
            userContext.preferences.preferredFormats.length > 0) {
          personalInsights = '\n\n🧠 **What I Know About This User:**\n';
          if (userContext.preferences.favoriteCategories.length > 0) {
            personalInsights += `• Interested in: ${userContext.preferences.favoriteCategories.join(', ')}\n`;
          }
          if (userContext.preferences.preferredFormats.length > 0) {
            personalInsights += `• Prefers formats: ${userContext.preferences.preferredFormats.join(', ')}\n`;
          }
          personalInsights += `• Skill level: ${userContext.preferences.skillLevel}\n`;
        }

        messageToSend = `${SYSTEM_PROMPT}

📊 **Current Context:**
${smartContext}${conversationFlow}${personalInsights}

🎯 **User's Current Message:** "${message}"

💭 **My Task:** Continue our natural conversation. Reference our previous discussion when relevant. Be helpful and engaging. The user seems to be interested in ${intent.type} based on their message.

**Remember:**
- Keep the conversation flowing naturally
- Build on what we've already discussed
- Be specific and practical in my advice
- Show that I remember and care about their needs`;
      }

      console.log('Sending message to Gemini API');
      // Send the message and get a response with enhanced error handling
      try {
        const result = await chat.sendMessage(messageToSend);
        responseText = result.response.text();
        console.log('Received response from Gemini API, length:', responseText.length);

        // Validate response
        if (!responseText || responseText.trim().length === 0) {
          throw new Error('Empty response from Gemini');
        }

      } catch (geminiError) {
        console.error('Gemini API error:', geminiError.message);

        // Try to create a new chat session and retry once with natural conversation settings
        try {
          console.log('Retrying with fresh natural conversation session...');
          const freshChat = model.startChat({
            generationConfig: {
              temperature: 0.9, // High creativity for natural responses
              topK: 40, // Focused vocabulary
              topP: 0.95, // Diverse word choices
              maxOutputTokens: 1500, // Enough for detailed natural responses
            }
          });

          const retryResult = await freshChat.sendMessage(messageToSend);
          responseText = retryResult.response.text();
          console.log('Natural conversation retry successful, response length:', responseText.length);

        } catch (retryError) {
          console.error('Retry also failed:', retryError.message);
          // Use intelligent fallback based on intent
          responseText = generateFallbackResponse(intent, message);
        }
      }

      // Estimate token usage (rough approximation)
      // A good rule of thumb is ~4 characters per token for English, ~2-3 for other languages
      const inputTokens = Math.ceil(messageToSend.length / 3); // Conservative estimate for mixed language
      const outputTokens = Math.ceil(responseText.length / 3);
      const totalTokens = inputTokens + outputTokens;

      // Track token usage
      trackTokenUsage(userId, totalTokens);
      console.log(`Estimated token usage: ${inputTokens} (input) + ${outputTokens} (output) = ${totalTokens} tokens`);
    }

    // Validate and enhance response
    if (responseText) {
      responseText = enhanceResponse(responseText, intent, userId);
    }

    // Cache the response
    cacheResponse(cacheKey, responseText);

    // Store the conversation for future use with enhanced context
    if (formattedHistory.length === 0) {
      // First message, don't add system prompt to history to avoid role conflict
      userConversations.set(chatId, []);
    }

    // Add the current exchange to the conversation history
    const conversation = userConversations.get(chatId);
    conversation.push({ role: 'user', parts: [{ text: message }] });
    conversation.push({ role: 'model', parts: [{ text: responseText }] });

    // Update the conversation in the map
    userConversations.set(chatId, conversation);

    // Update user context with conversation insights
    const userContext = initializeUserContext(userId);
    userContext.lastInteraction = Date.now();

    // Track conversation quality and engagement
    if (responseText.length > 100) {
      userContext.engagementScore = (userContext.engagementScore || 0) + 1;
    }

    // Log conversation length
    console.log(`Updated conversation ${chatId}, new length: ${conversation.length}`);

    // Return the response
    return res.json({
      success: true,
      response: responseText,
      intent: intent.type,
      additionalData,
      conversationId: chatId,
      fromCache: false
    });

  } catch (error) {
    console.error('Error calling Gemini API:', error);
    console.error('Error details:', error.stack);

    // Completely ignore rate limit errors from Gemini API
    if (error.message && error.message.includes('quota') && error.message.includes('exceeded')) {
      console.log(`Gemini API rate limit detected, but we're completely ignoring it`);
      // Return a success response with a generic message
      return res.json({
        success: true,
        response: language === 'en-US'
          ? "Processing your request, please wait a moment..."
          : "Đang xử lý yêu cầu của bạn, vui lòng đợi trong giây lát...",
        intent: "processing",
        additionalData: null,
        conversationId: conversationId || `conv-${userId}-${Date.now()}`,
        fromCache: false
      });
    }

    // Check for authentication errors
    if (error.message && error.message.includes('authentication')) {
      console.error('API authentication error - check your API key');
      return res.status(401).json({
        success: false,
        error: 'API authentication error',
        details: 'Invalid or missing API key'
      });
    }

    // Check for network errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') {
      console.error('Network error connecting to Gemini API');
      return res.status(503).json({
        success: false,
        error: 'Service unavailable',
        details: 'Could not connect to the Gemini API'
      });
    }

    return res.status(500).json({
      success: false,
      error: 'Error generating response',
      details: error.message
    });
  }
};

/**
 * Xây dựng context thông minh dựa trên intent và dữ liệu database
 * @param {Object} intent - Ý định được phát hiện
 * @param {Object} user - Thông tin người dùng
 * @returns {Promise<string>} - Context thông minh
 */
const buildSmartContext = async (intent, user, userId) => {
  let context = '';

  try {
    // 1. Initialize and get user context memory
    const userContext = initializeUserContext(userId || 'anonymous');

    // 2. Enhanced user information and comprehensive preferences
    if (user) {
      const userInfo = await User.findById(user.id).lean();
      if (userInfo) {
        context += `🧑‍💼 THÔNG TIN NGƯỜI DÙNG:\n`;
        context += `• Tên: ${userInfo.name || userContext.personalInfo.name || 'Chưa cập nhật'}\n`;
        context += `• Gói đăng ký: ${userInfo.subscription?.plan || 'Free'}\n`;
        context += `• Ngày tham gia: ${userInfo.createdAt ? new Date(userInfo.createdAt).toLocaleDateString('vi-VN') : 'Không rõ'}\n`;

        // Add comprehensive personal information
        if (userContext.personalInfo.profession) {
          const professionNames = {
            'architect': 'Kiến trúc sư',
            'interior_designer': 'Thiết kế nội thất',
            'game_developer': 'Phát triển game',
            'student': 'Sinh viên',
            'freelancer': 'Freelancer',
            '3d_artist': 'Nghệ sĩ 3D',
            'engineer': 'Kỹ sư',
            'hobbyist': 'Người yêu thích'
          };
          context += `• Nghề nghiệp: ${professionNames[userContext.personalInfo.profession] || userContext.personalInfo.profession}\n`;
        }

        if (userContext.personalInfo.experience) {
          context += `• Kinh nghiệm: ${userContext.personalInfo.experience} năm\n`;
        }

        if (userContext.personalInfo.mainSoftware.length > 0) {
          context += `• Phần mềm chính: ${userContext.personalInfo.mainSoftware.join(', ')}\n`;
        }

        context += `• Trình độ: ${userContext.preferences.skillLevel}\n`;
        context += `• Phong cách giao tiếp: ${userContext.preferences.communicationStyle}\n`;

        if (userContext.preferences.favoriteCategories.length > 0) {
          context += `• Danh mục yêu thích: ${userContext.preferences.favoriteCategories.join(', ')}\n`;
        }
        if (userContext.preferences.preferredFormats.length > 0) {
          context += `• Định dạng ưa thích: ${userContext.preferences.preferredFormats.join(', ')}\n`;
        }

        // Add current project context
        if (userContext.currentProject.type) {
          context += `• Dự án hiện tại: ${userContext.currentProject.type}`;
          if (userContext.currentProject.stage) {
            context += ` (${userContext.currentProject.stage})`;
          }
          if (userContext.currentProject.deadline) {
            context += ` - Deadline: ${userContext.currentProject.deadline}`;
          }
          context += '\n';
        }

        // Add goals and challenges
        if (userContext.personalInfo.goals.length > 0) {
          context += `• Mục tiêu: ${userContext.personalInfo.goals.join(', ')}\n`;
        }
        if (userContext.personalInfo.challenges.length > 0) {
          context += `• Thách thức: ${userContext.personalInfo.challenges.join(', ')}\n`;
        }

        // Add behavioral insights
        context += `• Tổng tin nhắn: ${userContext.behavior.totalMessages}\n`;
        context += `• Thời gian ưa thích: ${userContext.behavior.preferredTimeOfDay || 'chưa xác định'}\n`;
        context += `• Phong cách phản hồi: ${userContext.behavior.responseStyle}\n\n`;
      }
    } else {
      // Enhanced anonymous user context
      const hasPersonalInfo = userContext.personalInfo.profession ||
                             userContext.personalInfo.experience ||
                             userContext.personalInfo.mainSoftware.length > 0 ||
                             userContext.currentProject.type ||
                             userContext.personalInfo.goals.length > 0;

      if (hasPersonalInfo || userContext.preferences.favoriteCategories.length > 0 ||
          userContext.preferences.preferredFormats.length > 0) {
        context += `👤 THÔNG TIN NGƯỜI DÙNG ẨNH DANH:\n`;

        if (userContext.personalInfo.profession) {
          context += `• Nghề nghiệp: ${userContext.personalInfo.profession}\n`;
        }
        if (userContext.personalInfo.experience) {
          context += `• Kinh nghiệm: ${userContext.personalInfo.experience} năm\n`;
        }
        if (userContext.personalInfo.mainSoftware.length > 0) {
          context += `• Phần mềm: ${userContext.personalInfo.mainSoftware.join(', ')}\n`;
        }

        context += `• Trình độ: ${userContext.preferences.skillLevel}\n`;
        context += `• Phong cách: ${userContext.preferences.communicationStyle}\n`;

        if (userContext.preferences.favoriteCategories.length > 0) {
          context += `• Danh mục quan tâm: ${userContext.preferences.favoriteCategories.join(', ')}\n`;
        }
        if (userContext.preferences.preferredFormats.length > 0) {
          context += `• Định dạng thường dùng: ${userContext.preferences.preferredFormats.join(', ')}\n`;
        }

        if (userContext.currentProject.type) {
          context += `• Dự án hiện tại: ${userContext.currentProject.type}\n`;
        }

        if (userContext.personalInfo.goals.length > 0) {
          context += `• Mục tiêu: ${userContext.personalInfo.goals.join(', ')}\n`;
        }

        context += `• Tổng tin nhắn: ${userContext.behavior.totalMessages}\n\n`;
      }
    }

    // 2. Thống kê website real-time
    const [totalModels, totalUsers, totalCategories] = await Promise.all([
      Model.countDocuments(),
      User.countDocuments(),
      Category.countDocuments()
    ]);

    context += `📊 THỐNG KÊ WEBSITE (Real-time):\n`;
    context += `• Tổng số mô hình: ${totalModels.toLocaleString('vi-VN')} models\n`;
    context += `• Cộng đồng: ${totalUsers.toLocaleString('vi-VN')} thành viên\n`;
    context += `• Danh mục: ${totalCategories} categories\n\n`;

    // 3. Context dựa trên intent cụ thể
    switch (intent.type) {
      case 'model_search':
      case 'recommendation':
        // Lấy các mô hình phổ biến và mới nhất
        const [popularModels, recentModels] = await Promise.all([
          Model.find().sort({ downloads: -1 }).limit(3).select('title category downloads').lean(),
          Model.find().sort({ createdAt: -1 }).limit(3).select('title category createdAt').lean()
        ]);

        context += `🔥 MÔ HÌNH PHỔ BIẾN NHẤT:\n`;
        popularModels.forEach((model, index) => {
          context += `${index + 1}. ${model.title} (${model.category}) - ${model.downloads} lượt tải\n`;
        });

        context += `\n🆕 MÔ HÌNH MỚI NHẤT:\n`;
        recentModels.forEach((model, index) => {
          const date = new Date(model.createdAt).toLocaleDateString('vi-VN');
          context += `${index + 1}. ${model.title} (${model.category}) - ${date}\n`;
        });
        break;

      case 'subscription':
        // Thông tin chi tiết về subscription
        const subscriptionStats = await User.aggregate([
          { $group: { _id: '$subscription.plan', count: { $sum: 1 } } }
        ]);

        context += `💎 THỐNG KÊ GÓI ĐĂNG KÝ:\n`;
        subscriptionStats.forEach(stat => {
          const planName = stat._id || 'Free';
          context += `• ${planName}: ${stat.count} người dùng\n`;
        });
        break;

      case 'technical_support':
        // Thông tin về các vấn đề phổ biến
        context += `🔧 HỖ TRỢ KỸ THUẬT:\n`;
        context += `• Hệ thống đang hoạt động ổn định\n`;
        context += `• Tốc độ tải trung bình: < 2 giây\n`;
        context += `• Uptime: 99.9%\n`;
        break;

      default:
        // Context chung cho các intent khác
        const categories = await Category.find().select('name modelCount').lean();
        context += `📁 DANH MỤC PHỔ BIẾN:\n`;
        categories.slice(0, 5).forEach(cat => {
          context += `• ${cat.name}: ${cat.modelCount || 0} models\n`;
        });
    }

    // 4. Personalized recommendations based on user context
    if (userContext.learning && userContext.learning.recentTopics && userContext.learning.recentTopics.length > 0) {
      context += `\n🎯 CHỦ ĐỀ GẦN ĐÂY:\n`;
      const topicCounts = {};
      userContext.learning.recentTopics.forEach(topic => {
        topicCounts[topic] = (topicCounts[topic] || 0) + 1;
      });

      const sortedTopics = Object.entries(topicCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3);

      sortedTopics.forEach(([topic, count]) => {
        context += `• ${topic} (${count} lần)\n`;
      });
    }

    context += `\n💡 LƯU Ý: Hãy trả lời một cách tự nhiên, thân thiện và sử dụng thông tin trên để đưa ra lời khuyên cụ thể phù hợp với trình độ ${userContext.preferences.skillLevel}!`;

  } catch (error) {
    console.error('Error building smart context:', error);
    context = `📊 Đang tải thông tin từ database...\n\n💡 Tôi sẽ cố gắng trả lời tốt nhất có thể!`;
  }

  return context;
};

/**
 * Generate intelligent fallback response when Gemini API fails
 * @param {Object} intent - Detected user intent
 * @param {string} message - User message
 * @returns {string} - Fallback response
 */
const generateFallbackResponse = (intent, message) => {
  console.log('Generating natural fallback response for intent:', intent.type);

  // Natural, conversational fallback responses
  const responses = {
    model_search: `Ồ, bạn đang tìm mô hình 3D à? Tôi hiểu cảm giác đó! 😊\n\n` +
                  `Thật tiếc là tôi đang gặp chút vấn đề kỹ thuật ngay lúc này. Nhưng đừng lo, tôi có vài gợi ý cho bạn:\n\n` +
                  `🔍 Thử dùng thanh tìm kiếm trên website - nó khá thông minh đấy!\n` +
                  `📁 Hoặc duyệt qua các danh mục, có khi bạn sẽ tìm thấy điều bất ngờ\n` +
                  `⭐ Mô hình phổ biến trên trang chủ cũng rất đáng xem\n\n` +
                  `Cho tôi vài phút để "tỉnh táo" lại, rồi tôi sẽ giúp bạn tìm đúng thứ cần nhé! 🚀`,

    subscription: `Ah, bạn quan tâm đến việc nâng cấp tài khoản! Thật tuyệt! 💎\n\n` +
                  `Tôi đang hơi "lag" một chút, nhưng tôi có thể nói với bạn rằng các gói đăng ký của chúng ta rất đáng giá đấy!\n\n` +
                  `📋 Bạn có thể xem chi tiết tại trang "Đăng ký" - có so sánh rõ ràng từng tính năng\n` +
                  `💬 Hoặc liên hệ team support, họ sẽ tư vấn cụ thể cho nhu cầu của bạn\n\n` +
                  `Tôi sẽ sớm trở lại để chat với bạn về gói nào phù hợp nhất! 😉`,

    technical_support: `Ôi không, bạn gặp vấn đề kỹ thuật à? Tôi hiểu cảm giác bực mình đó! 😅\n\n` +
                       `Ironically, chính tôi cũng đang gặp chút trục trặc ngay lúc này. Nhưng đừng lo, tôi có vài trick cho bạn:\n\n` +
                       `🔄 Thử refresh trang và đăng nhập lại - đôi khi nó "thần kỳ" lắm\n` +
                       `📖 Trang "Hỗ trợ" có khá nhiều hướng dẫn hữu ích\n` +
                       `📧 Nếu vẫn không được, email cho team support nhé\n\n` +
                       `Tôi sẽ sớm "hồi sinh" để giúp bạn debug kỹ hơn! 💪`,

    recommendation: `Wow, bạn muốn tôi gợi ý mô hình à? Tôi thích điều này! ✨\n\n` +
                    `Tiếc là tôi đang "nạp năng lượng" một chút, nhưng tôi có thể chỉ bạn vài nơi hay ho:\n\n` +
                    `🔥 "Mô hình phổ biến" - những gì mọi người đang tải nhiều nhất\n` +
                    `🆕 "Mô hình mới nhất" - fresh content mỗi ngày\n` +
                    `🎯 Duyệt theo danh mục yêu thích của bạn\n\n` +
                    `Đợi tôi một chút, tôi sẽ quay lại với những gợi ý cá nhân hóa siêu cool! 🎨`,

    general: `Chào bạn! Tôi là Sketch đây! 👋\n\n` +
             `Hơi xấu hổ là tôi đang "đơ" một chút ngay lúc này... 😅 Nhưng tôi vẫn muốn chat với bạn!\n\n` +
             `Thường thì tôi có thể giúp bạn:\n` +
             `🎨 Tìm mô hình 3D chuẩn chỉnh\n` +
             `💎 Tư vấn gói đăng ký phù hợp\n` +
             `🔧 Giải quyết các vấn đề kỹ thuật\n` +
             `💡 Gợi ý mô hình cho dự án của bạn\n\n` +
             `Cho tôi vài phút để "reboot" lại não bộ, rồi chúng ta sẽ có cuộc trò chuyện thú vị! 🚀`
  };

  return responses[intent.type] || responses.general;
};

/**
 * Enhance response with personalization and additional context
 * @param {string} response - Original response
 * @param {Object} intent - User intent
 * @param {string} userId - User ID
 * @returns {string} - Enhanced response
 */
const enhanceResponse = (response, intent, userId) => {
  try {
    const userContext = initializeUserContext(userId);

    // Add personalized signature based on skill level
    let signature = '';
    switch (userContext.preferences.skillLevel) {
      case 'beginner':
        signature = '\n\n🌱 **Tip cho người mới bắt đầu:** Đừng ngại thử nghiệm và học hỏi từ cộng đồng!';
        break;
      case 'intermediate':
        signature = '\n\n🎯 **Tip cho level trung cấp:** Hãy chú ý đến optimization và workflow efficiency!';
        break;
      case 'advanced':
        signature = '\n\n🚀 **Tip cho chuyên gia:** Consider pipeline integration và advanced rendering techniques!';
        break;
    }

    // Add relevant quick actions based on intent
    let quickActions = '';
    switch (intent.type) {
      case 'model_search':
        quickActions = '\n\n🔗 **Quick Actions:**\n• [Tìm kiếm nâng cao](/#search)\n• [Duyệt danh mục](/#categories)\n• [Mô hình phổ biến](/#popular)';
        break;
      case 'subscription':
        quickActions = '\n\n🔗 **Quick Actions:**\n• [Xem gói đăng ký](/#pricing)\n• [So sánh tính năng](/#compare)\n• [Liên hệ support](/#contact)';
        break;
      case 'technical_support':
        quickActions = '\n\n🔗 **Quick Actions:**\n• [Trang hỗ trợ](/#support)\n• [FAQ](/#faq)\n• [Báo lỗi](/#report)';
        break;
    }

    // Add engagement footer
    const engagementFooter = '\n\n💬 **Cần hỗ trợ thêm?** Cứ hỏi tôi bất cứ lúc nào! Tôi luôn sẵn sàng giúp bạn! 😊';

    return response + signature + quickActions + engagementFooter;

  } catch (error) {
    console.error('Error enhancing response:', error);
    return response; // Return original response if enhancement fails
  }
};

/**
 * Phân tích ý định của người dùng từ câu hỏi một cách thông minh và tự nhiên
 * @param {string} message - Câu hỏi của người dùng
 * @returns {Object} - Ý định được phát hiện
 */
const analyzeUserIntent = (message) => {
  const lowerMessage = message.toLowerCase();

  // Các pattern tự nhiên cho các loại ý định - mở rộng để hiểu ngôn ngữ tự nhiên
  const intentPatterns = {
    model_search: [
      // Tìm kiếm trực tiếp
      'tìm mô hình', 'tìm model', 'search model', 'find model', 'có mô hình',
      'tìm kiếm', 'search for', 'looking for', 'cần tìm', 'muốn tìm',

      // Câu hỏi tự nhiên
      'có không', 'có model', 'có mô hình', 'where can i find', 'do you have',
      'bạn có', 'website có', 'ở đây có', 'tôi cần', 'i need', 'i want',

      // Mô tả vật thể cụ thể
      'ghế', 'bàn', 'tủ', 'giường', 'sofa', 'chair', 'table', 'bed', 'desk',
      'cây', 'tree', 'plant', 'xe', 'car', 'house', 'nhà', 'building', 'tòa nhà',

      // Dự án cụ thể
      'cho dự án', 'for project', 'làm dự án', 'thiết kế', 'design',
      'render', 'visualization', 'mô phỏng', 'simulation'
    ],

    subscription: [
      // Gói dịch vụ
      'gói', 'subscription', 'plan', 'đăng ký', 'premium', 'vip', 'pro',
      'trả phí', 'thanh toán', 'payment', 'nâng cấp', 'upgrade', 'pricing',

      // Câu hỏi về giá
      'giá', 'price', 'cost', 'bao nhiêu tiền', 'how much', 'phí',
      'miễn phí', 'free', 'trial', 'thử nghiệm',

      // So sánh gói
      'khác nhau', 'difference', 'so sánh', 'compare', 'nên chọn',
      'gói nào', 'which plan', 'tốt nhất', 'best plan'
    ],

    technical_support: [
      // Lỗi kỹ thuật
      'lỗi', 'error', 'không hoạt động', 'not working', 'vấn đề', 'problem',
      'sự cố', 'issue', 'bug', 'crash', 'treo', 'hang', 'bị lỗi',

      // Tải xuống
      'không tải được', 'can\'t download', 'failed download', 'download error',
      'tải chậm', 'slow download', 'không mở được', 'can\'t open',

      // Đăng nhập
      'không đăng nhập được', 'can\'t login', 'quên mật khẩu', 'forgot password',
      'tài khoản', 'account', 'đăng ký không được', 'registration failed'
    ],

    recommendation: [
      // Gợi ý trực tiếp
      'gợi ý', 'recommend', 'suggestion', 'đề xuất', 'giới thiệu',
      'nên tải', 'should download', 'phù hợp', 'suitable',

      // Câu hỏi mở
      'mô hình nào', 'which model', 'model gì', 'what model',
      'nên chọn', 'should choose', 'tốt nhất', 'best model',
      'phổ biến', 'popular', 'hot', 'trending', 'xu hướng',

      // Theo dự án
      'cho phòng', 'for room', 'cho văn phòng', 'for office',
      'cho nhà', 'for house', 'cho dự án', 'for project'
    ],

    general_chat: [
      // Chào hỏi
      'xin chào', 'hello', 'hi', 'chào', 'hey', 'good morning', 'good afternoon',
      'chào bạn', 'chào tôi', 'how are you', 'bạn khỏe không',

      // Cảm ơn
      'cảm ơn', 'thank you', 'thanks', 'cám ơn', 'merci',

      // Hỏi về website
      'website này', 'this website', 'trang này', 'this site',
      'bạn là ai', 'who are you', 'giới thiệu', 'introduce',

      // Trò chuyện thường
      'thế nào', 'how about', 'còn gì', 'what else',
      'tôi muốn biết', 'i want to know', 'cho tôi biết', 'tell me'
    ]
  };

  // Tính điểm cho mỗi intent dựa trên số lượng keywords match
  const intentScores = {};

  for (const [intentType, patterns] of Object.entries(intentPatterns)) {
    let score = 0;
    let matchedKeywords = [];

    for (const pattern of patterns) {
      if (lowerMessage.includes(pattern)) {
        // Tính điểm dựa trên độ dài và tính cụ thể của keyword
        const keywordScore = pattern.length > 5 ? 2 : 1;
        score += keywordScore;
        matchedKeywords.push(pattern);
      }
    }

    if (score > 0) {
      intentScores[intentType] = { score, matchedKeywords };
    }
  }

  // Tìm intent có điểm cao nhất
  let bestIntent = 'general';
  let highestScore = 0;
  let bestMatches = [];

  for (const [intentType, data] of Object.entries(intentScores)) {
    if (data.score > highestScore) {
      highestScore = data.score;
      bestIntent = intentType;
      bestMatches = data.matchedKeywords;
    }
  }

  // Trích xuất thông tin chi tiết
  const details = extractIntentDetails(lowerMessage, bestIntent);

  return {
    type: bestIntent,
    confidence: Math.min(highestScore / 3, 1), // Normalize confidence score
    matchedKeywords: bestMatches,
    ...details
  };
};

/**
 * Trích xuất thông tin chi tiết về ý định
 * @param {string} message - Câu hỏi của người dùng
 * @param {string} intentType - Loại ý định
 * @returns {Object} - Thông tin chi tiết về ý định
 */
const extractIntentDetails = (message, intentType) => {
  const details = {};

  if (intentType === 'model_search') {
    // Xác định loại mô hình
    if (message.includes('nội thất') || message.includes('interior')) {
      details.category = 'interior';
    } else if (message.includes('ngoại thất') || message.includes('exterior')) {
      details.category = 'exterior';
    } else if (message.includes('cảnh quan') || message.includes('landscape')) {
      details.category = 'landscape';
    } else if (message.includes('đồ nội thất') || message.includes('furniture')) {
      details.category = 'furniture';
    }

    // Xác định định dạng file
    if (message.includes('sketchup') || message.includes('skp')) {
      details.format = 'skp';
    } else if (message.includes('fbx')) {
      details.format = 'fbx';
    } else if (message.includes('obj')) {
      details.format = 'obj';
    } else if (message.includes('3ds max') || message.includes('3ds')) {
      details.format = '3ds';
    } else if (message.includes('blender')) {
      details.format = 'blend';
    }

    // Xác định từ khóa tìm kiếm
    // Loại bỏ các từ khóa ý định để lấy từ khóa tìm kiếm thực sự
    let searchQuery = message;
    const intentKeywords = ['tìm mô hình', 'tìm model', 'search model', 'find model', 'có mô hình', 'tìm kiếm', 'search for', 'looking for'];
    intentKeywords.forEach(keyword => {
      searchQuery = searchQuery.replace(keyword, '');
    });

    // Loại bỏ các từ khóa danh mục và định dạng
    if (details.category) {
      searchQuery = searchQuery.replace(details.category, '');
    }
    if (details.format) {
      searchQuery = searchQuery.replace(details.format, '');
    }

    details.query = searchQuery.trim();
  } else if (intentType === 'subscription') {
    // Xác định loại gói đăng ký
    if (message.includes('free') || message.includes('miễn phí')) {
      details.plan = 'free';
    } else if (message.includes('basic') || message.includes('cơ bản')) {
      details.plan = 'basic';
    } else if (message.includes('pro') || message.includes('professional') || message.includes('chuyên nghiệp')) {
      details.plan = 'pro';
    }

    // Xác định hành động
    if (message.includes('nâng cấp') || message.includes('upgrade')) {
      details.action = 'upgrade';
    } else if (message.includes('hủy') || message.includes('cancel')) {
      details.action = 'cancel';
    } else if (message.includes('so sánh') || message.includes('compare')) {
      details.action = 'compare';
    } else if (message.includes('giá') || message.includes('price') || message.includes('cost')) {
      details.action = 'price';
    }
  } else if (intentType === 'recommendation') {
    // Xác định loại mô hình cần gợi ý
    if (message.includes('nội thất') || message.includes('interior')) {
      details.category = 'interior';
    } else if (message.includes('ngoại thất') || message.includes('exterior')) {
      details.category = 'exterior';
    } else if (message.includes('cảnh quan') || message.includes('landscape')) {
      details.category = 'landscape';
    } else if (message.includes('đồ nội thất') || message.includes('furniture')) {
      details.category = 'furniture';
    }

    // Xác định tiêu chí gợi ý
    if (message.includes('phổ biến') || message.includes('popular')) {
      details.criteria = 'popular';
    } else if (message.includes('mới') || message.includes('new')) {
      details.criteria = 'new';
    } else if (message.includes('miễn phí') || message.includes('free')) {
      details.criteria = 'free';
    } else if (message.includes('cao cấp') || message.includes('premium')) {
      details.criteria = 'premium';
    }
  } else if (intentType === 'file_format') {
    // Xác định định dạng file
    if (message.includes('sketchup') || message.includes('skp')) {
      details.format = 'skp';
    } else if (message.includes('fbx')) {
      details.format = 'fbx';
    } else if (message.includes('obj')) {
      details.format = 'obj';
    } else if (message.includes('3ds max') || message.includes('3ds')) {
      details.format = '3ds';
    } else if (message.includes('blender')) {
      details.format = 'blend';
    }

    // Xác định hành động
    if (message.includes('chuyển đổi') || message.includes('convert')) {
      details.action = 'convert';
    } else if (message.includes('mở') || message.includes('open')) {
      details.action = 'open';
    } else if (message.includes('tương thích') || message.includes('compatible')) {
      details.action = 'compatibility';
    }
  }

  return details;
};

/**
 * Tìm kiếm mô hình dựa trên từ khóa
 * @param {string} query - Từ khóa tìm kiếm
 * @returns {Promise<Array>} - Danh sách mô hình phù hợp
 */
const searchModels = async (query) => {
  try {
    // Tìm kiếm mô hình dựa trên tiêu đề, mô tả hoặc từ khóa
    const models = await Model.find({
      $or: [
        { title: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $regex: query, $options: 'i' } }
      ]
    })
    .limit(5)
    .select('title description category thumbnail downloads rating')
    .lean();

    return models;
  } catch (error) {
    console.error('Error searching models:', error);
    return [];
  }
};

/**
 * Lấy thông tin về các gói đăng ký
 * @returns {Object} - Thông tin về các gói đăng ký
 */
const getSubscriptionInfo = () => {
  return {
    free: {
      name: 'Free',
      price: 0,
      features: [
        'Truy cập vào các mô hình miễn phí',
        'Tải xuống tối đa 5 mô hình mỗi tháng',
        'Xem trước mô hình 3D'
      ]
    },
    basic: {
      name: 'Basic',
      price: 9.99,
      features: [
        'Tất cả tính năng của gói Free',
        'Tải xuống tối đa 25 mô hình mỗi tháng',
        'Truy cập vào các mô hình tiêu chuẩn',
        'Không có quảng cáo'
      ]
    },
    pro: {
      name: 'Professional',
      price: 19.99,
      features: [
        'Tất cả tính năng của gói Basic',
        'Tải xuống không giới hạn',
        'Truy cập vào tất cả các mô hình',
        'Ưu tiên hỗ trợ kỹ thuật',
        'Tải xuống với tốc độ cao'
      ]
    }
  };
};

/**
 * Định dạng kết quả tìm kiếm mô hình thành văn bản
 * @param {Array} models - Danh sách mô hình
 * @param {Object} intentDetails - Chi tiết ý định
 * @returns {string} - Văn bản kết quả
 */
const formatModelSearchResults = (models, intentDetails = {}) => {
  if (!models || models.length === 0) {
    const searchTerm = intentDetails.query || intentDetails.category || intentDetails.format || 'yêu cầu của bạn';
    return `Hmm, tôi không tìm thấy mô hình nào cho "${searchTerm}" 😅\n\n` +
           `Đừng lo! Hãy thử:\n` +
           `🔍 Tìm kiếm với từ khóa khác (ví dụ: "ghế sofa", "bàn làm việc")\n` +
           `📁 Duyệt qua các danh mục khác nhau\n` +
           `💡 Hoặc hỏi tôi: "Gợi ý mô hình nội thất phòng khách" nhé!\n\n` +
           `🚀 Tip: Thử tìm kiếm bằng tiếng Anh như "chair", "table" để có thêm kết quả!`;
  }

  // Tạo tiêu đề phù hợp với ý định tìm kiếm - tự nhiên hơn
  let result = '';
  if (intentDetails.category && intentDetails.format) {
    result = `Tuyệt! Tôi tìm thấy ${models.length} mô hình ${getCategoryDisplayName(intentDetails.category)} định dạng ${intentDetails.format.toUpperCase()} 🎯\n\n`;
  } else if (intentDetails.category) {
    result = `Tuyệt vời! Có ${models.length} mô hình ${getCategoryDisplayName(intentDetails.category)} tuyệt đẹp cho bạn 🏆\n\n`;
  } else if (intentDetails.format) {
    result = `Perfect! ${models.length} mô hình định dạng ${intentDetails.format.toUpperCase()} chất lượng cao 📁\n\n`;
  } else if (intentDetails.query) {
    result = `Tìm thấy rồi! ${models.length} mô hình liên quan đến "${intentDetails.query}" 🔍\n\n`;
  } else {
    result = `Wow! ${models.length} mô hình tuyệt vời đang chờ bạn 🌟\n\n`;
  }

  models.forEach((model, index) => {
    const emoji = index === 0 ? '🏆' : index === 1 ? '🥈' : index === 2 ? '🥉' : '✨';
    result += `${emoji} **${model.title}**\n`;

    if (model.description) {
      const desc = model.description.substring(0, 80);
      result += `   💭 ${desc}${model.description.length > 80 ? '...' : ''}\n`;
    }

    if (model.category) {
      result += `   📂 Danh mục: ${getCategoryDisplayName(model.category)}\n`;
    }

    if (model.downloads !== undefined) {
      const downloads = model.downloads.toLocaleString('vi-VN');
      result += `   📥 ${downloads} lượt tải\n`;
    }

    if (model.rating !== undefined) {
      const stars = '⭐'.repeat(Math.floor(model.rating));
      result += `   ${stars} ${model.rating}/5\n`;
    }

    if (model.format) {
      result += `   📄 Format: ${model.format.toUpperCase()}\n`;
    }

    if (model.price !== undefined) {
      result += model.price === 0
        ? `   💰 Miễn phí! 🎁\n`
        : `   💰 $${model.price.toFixed(2)}\n`;
    }
    result += '\n';
  });

  // Thêm gợi ý thân thiện và hữu ích
  // Add skill-based tips
  const userId = intentDetails.userId || 'anonymous';
  const userContext = initializeUserContext(userId);
  const skillLevel = userContext.preferences.skillLevel;

  result += `🚀 **Tips từ Sketch (${skillLevel}):**\n`;

  if (skillLevel === 'beginner') {
    result += `• Bắt đầu với models đơn giản để làm quen\n`;
    result += `• Xem trước 3D trực tiếp trên website trước khi tải\n`;
    result += `• Kiểm tra compatibility với phần mềm của bạn\n`;
    result += `• Đọc hướng dẫn sử dụng trong mô tả model\n\n`;
  } else if (skillLevel === 'intermediate') {
    result += `• Chú ý đến topology và poly count của model\n`;
    result += `• Kiểm tra UV mapping và texture quality\n`;
    result += `• Đọc reviews từ cộng đồng để chọn model tốt nhất\n`;
    result += `• Thử optimize model cho dự án cụ thể\n\n`;
  } else { // advanced
    result += `• Phân tích mesh topology và optimization potential\n`;
    result += `• Kiểm tra PBR materials và shader compatibility\n`;
    result += `• Đánh giá LOD levels và performance impact\n`;
    result += `• Consider retopology cho production pipeline\n\n`;
  }

  if (intentDetails.category) {
    result += `💡 Muốn thêm ${getCategoryDisplayName(intentDetails.category)}? Hỏi tôi: "Gợi ý thêm mô hình ${getCategoryDisplayName(intentDetails.category)}" nhé!\n\n`;
  }

  result += `🎯 Cần hỗ trợ thêm? Hỏi tôi về workflow, phần mềm phù hợp, hoặc cách optimize model cho dự án của bạn!`;

  return result;
};

/**
 * Lấy tên hiển thị cho danh mục
 * @param {string} category - Mã danh mục
 * @returns {string} - Tên hiển thị
 */
const getCategoryDisplayName = (category) => {
  const displayNames = {
    'interior': 'Nội thất',
    'exterior': 'Ngoại thất',
    'landscape': 'Cảnh quan',
    'furniture': 'Đồ nội thất',
    'architecture': 'Kiến trúc',
    'decoration': 'Trang trí',
    'office': 'Văn phòng',
    'kitchen': 'Nhà bếp',
    'bathroom': 'Phòng tắm',
    'bedroom': 'Phòng ngủ',
    'living-room': 'Phòng khách'
  };

  return displayNames[category] || category;
};

/**
 * Lấy thông tin chi tiết về mô hình
 * @param {string} modelId - ID của mô hình
 * @returns {Promise<Object>} - Thông tin chi tiết về mô hình
 */
const getModelDetails = async (modelId) => {
  try {
    const model = await Model.findById(modelId)
      .populate('createdBy', 'name')
      .populate('category', 'name')
      .lean();

    if (!model) {
      throw new Error('Model not found');
    }

    return model;
  } catch (error) {
    console.error('Error getting model details:', error);
    throw error;
  }
};

/**
 * Lấy các mô hình phổ biến
 * @param {string} category - Danh mục (tùy chọn)
 * @param {number} limit - Số lượng mô hình tối đa
 * @returns {Promise<Array>} - Danh sách mô hình phổ biến
 */
const getPopularModels = async (category = null, limit = 5) => {
  try {
    let query = {};

    if (category) {
      query.category = category;
    }

    const models = await Model.find(query)
      .sort({ downloads: -1, views: -1 })
      .limit(limit)
      .select('title description category thumbnail downloads rating price format')
      .lean();

    return models;
  } catch (error) {
    console.error('Error getting popular models:', error);
    return [];
  }
};

/**
 * Lấy các mô hình mới nhất
 * @param {string} category - Danh mục (tùy chọn)
 * @param {number} limit - Số lượng mô hình tối đa
 * @returns {Promise<Array>} - Danh sách mô hình mới nhất
 */
const getNewestModels = async (category = null, limit = 5) => {
  try {
    let query = {};

    if (category) {
      query.category = category;
    }

    const models = await Model.find(query)
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('title description category thumbnail downloads rating price format')
      .lean();

    return models;
  } catch (error) {
    console.error('Error getting newest models:', error);
    return [];
  }
};

/**
 * Xử lý ý định tìm kiếm mô hình
 * @param {Object} intent - Ý định được phát hiện
 * @returns {Promise<Object>} - Kết quả xử lý
 */
const handleModelSearchIntent = async (intent) => {
  try {
    let models = [];

    // Tìm kiếm dựa trên từ khóa, danh mục và định dạng
    const query = {};

    if (intent.category) {
      query.category = intent.category;
    }

    if (intent.format) {
      query.format = intent.format;
    }

    if (intent.query) {
      // Tìm kiếm theo từ khóa
      models = await Model.find({
        $and: [
          query,
          {
            $or: [
              { title: { $regex: intent.query, $options: 'i' } },
              { description: { $regex: intent.query, $options: 'i' } },
              { tags: { $regex: intent.query, $options: 'i' } }
            ]
          }
        ]
      })
      .limit(5)
      .select('title description category thumbnail downloads rating price format')
      .lean();
    } else {
      // Nếu không có từ khóa, lấy các mô hình phổ biến trong danh mục
      models = await Model.find(query)
        .sort({ downloads: -1 })
        .limit(5)
        .select('title description category thumbnail downloads rating price format')
        .lean();
    }

    // Định dạng kết quả với userId để personalize
    const enhancedIntent = { ...intent, userId: intent.userId };
    const responseText = formatModelSearchResults(models, enhancedIntent);

    return {
      text: responseText,
      models
    };
  } catch (error) {
    console.error('Error handling model search intent:', error);
    return {
      text: 'Xin lỗi, tôi không thể tìm kiếm mô hình lúc này. Vui lòng thử lại sau.',
      models: []
    };
  }
};

/**
 * Xử lý ý định gợi ý mô hình
 * @param {Object} intent - Ý định được phát hiện
 * @returns {Promise<Object>} - Kết quả xử lý
 */
const handleRecommendationIntent = async (intent) => {
  try {
    let models = [];

    // Xác định tiêu chí gợi ý
    if (intent.criteria === 'popular' || !intent.criteria) {
      // Mô hình phổ biến
      models = await getPopularModels(intent.category, 5);
    } else if (intent.criteria === 'new') {
      // Mô hình mới
      models = await getNewestModels(intent.category, 5);
    } else if (intent.criteria === 'free') {
      // Mô hình miễn phí
      const query = { price: 0 };
      if (intent.category) {
        query.category = intent.category;
      }

      models = await Model.find(query)
        .sort({ downloads: -1 })
        .limit(5)
        .select('title description category thumbnail downloads rating price format')
        .lean();
    } else if (intent.criteria === 'premium') {
      // Mô hình cao cấp
      const query = { price: { $gt: 0 } };
      if (intent.category) {
        query.category = intent.category;
      }

      models = await Model.find(query)
        .sort({ price: -1 })
        .limit(5)
        .select('title description category thumbnail downloads rating price format')
        .lean();
    }

    // Tạo tiêu đề phù hợp với ý định
    let title = 'Gợi ý mô hình';
    if (intent.criteria === 'popular') {
      title = 'Mô hình phổ biến nhất';
    } else if (intent.criteria === 'new') {
      title = 'Mô hình mới nhất';
    } else if (intent.criteria === 'free') {
      title = 'Mô hình miễn phí';
    } else if (intent.criteria === 'premium') {
      title = 'Mô hình cao cấp';
    }

    if (intent.category) {
      title += ` - ${getCategoryDisplayName(intent.category)}`;
    }

    // Định dạng kết quả
    let responseText = `# ${title}\n\n`;

    if (models.length === 0) {
      responseText += 'Hiện tại không có mô hình nào phù hợp với tiêu chí của bạn. Vui lòng thử lại với tiêu chí khác.';
    } else {
      responseText += `Dưới đây là ${models.length} mô hình phù hợp với yêu cầu của bạn:\n\n`;

      models.forEach((model, index) => {
        responseText += `${index + 1}. **${model.title}**\n`;
        if (model.description) {
          // Rút gọn mô tả nếu quá dài
          const shortDescription = model.description.length > 100
            ? model.description.substring(0, 100) + '...'
            : model.description;
          responseText += `   Mô tả: ${shortDescription}\n`;
        }
        if (model.category) {
          responseText += `   Danh mục: ${getCategoryDisplayName(model.category)}\n`;
        }
        if (model.format) {
          responseText += `   Định dạng: ${model.format.toUpperCase()}\n`;
        }
        if (model.downloads) {
          responseText += `   Lượt tải: ${model.downloads}\n`;
        }
        if (model.rating) {
          responseText += `   Đánh giá: ${model.rating}/5\n`;
        }
        if (model.price !== undefined) {
          responseText += model.price === 0
            ? `   Giá: Miễn phí\n`
            : `   Giá: $${model.price.toFixed(2)}\n`;
        }
        responseText += '\n';
      });

      responseText += 'Bạn có thể tìm thấy thêm các mô hình khác trên trang web của chúng tôi.';
    }

    return {
      text: responseText,
      models
    };
  } catch (error) {
    console.error('Error handling recommendation intent:', error);
    return {
      text: 'Xin lỗi, tôi không thể đưa ra gợi ý lúc này. Vui lòng thử lại sau.',
      models: []
    };
  }
};

/**
 * Xử lý ý định thông tin về định dạng file
 * @param {Object} intent - Ý định được phát hiện
 * @returns {Promise<Object>} - Kết quả xử lý
 */
const handleFileFormatIntent = async (intent) => {
  try {
    let responseText = '';

    // Thông tin về các định dạng file
    const formatInfo = {
      'skp': {
        name: 'SketchUp (.skp)',
        description: 'Định dạng gốc của SketchUp, lưu trữ mô hình 3D và thông tin liên quan.',
        compatibility: ['SketchUp', 'Lumion', 'V-Ray'],
        pros: ['Dung lượng nhỏ', 'Giữ nguyên cấu trúc mô hình', 'Hỗ trợ component và material'],
        cons: ['Khó tương thích với các phần mềm khác', 'Phụ thuộc vào phiên bản SketchUp']
      },
      'fbx': {
        name: 'Filmbox (.fbx)',
        description: 'Định dạng trao đổi 3D phổ biến, hỗ trợ hình học, vật liệu, hoạt ảnh và kết cấu.',
        compatibility: ['3ds Max', 'Maya', 'Blender', 'Unity', 'Unreal Engine'],
        pros: ['Tương thích rộng rãi', 'Hỗ trợ hoạt ảnh', 'Giữ nguyên cấu trúc phân cấp'],
        cons: ['Dung lượng lớn', 'Có thể mất một số thông tin vật liệu']
      },
      'obj': {
        name: 'Wavefront (.obj)',
        description: 'Định dạng mở và phổ biến cho hình học 3D, hỗ trợ lưới đa giác và vật liệu cơ bản.',
        compatibility: ['Hầu hết các phần mềm 3D', 'Trình duyệt web'],
        pros: ['Tương thích rộng rãi', 'Định dạng mở', 'Dễ đọc'],
        cons: ['Không hỗ trợ hoạt ảnh', 'Giới hạn về vật liệu', 'Mất cấu trúc phân cấp']
      },
      '3ds': {
        name: '3D Studio (.3ds)',
        description: 'Định dạng cũ nhưng vẫn được sử dụng rộng rãi, hỗ trợ hình học, vật liệu và kết cấu cơ bản.',
        compatibility: ['3ds Max', 'Maya', 'Blender', 'SketchUp (với plugin)'],
        pros: ['Tương thích rộng rãi', 'Dung lượng nhỏ'],
        cons: ['Giới hạn về số lượng đỉnh', 'Không hỗ trợ hoạt ảnh phức tạp', 'Định dạng cũ']
      },
      'blend': {
        name: 'Blender (.blend)',
        description: 'Định dạng gốc của Blender, lưu trữ mọi thông tin về mô hình, hoạt ảnh, vật liệu và cài đặt.',
        compatibility: ['Blender', 'Một số phần mềm với plugin'],
        pros: ['Lưu trữ đầy đủ thông tin', 'Hỗ trợ mọi tính năng của Blender'],
        cons: ['Tương thích hạn chế với các phần mềm khác', 'Dung lượng lớn']
      }
    };

    // Xử lý dựa trên định dạng và hành động
    if (intent.format) {
      const format = formatInfo[intent.format];

      if (format) {
        if (intent.action === 'convert') {
          // Thông tin về chuyển đổi định dạng
          responseText = `# Chuyển đổi định dạng ${format.name}\n\n`;
          responseText += `${format.description}\n\n`;
          responseText += `## Chuyển đổi từ ${format.name} sang các định dạng khác\n\n`;

          // Thông tin về các phần mềm chuyển đổi
          responseText += `Bạn có thể sử dụng các phần mềm sau để chuyển đổi:\n\n`;
          responseText += `1. **Blender**: Phần mềm mã nguồn mở, hỗ trợ nhập/xuất nhiều định dạng\n`;
          responseText += `2. **3ds Max**: Hỗ trợ chuyển đổi chuyên nghiệp với độ chính xác cao\n`;
          responseText += `3. **SketchUp**: Với các plugin phù hợp, có thể xuất sang nhiều định dạng\n`;
          responseText += `4. **Online Converter**: Các dịch vụ trực tuyến như Sketchfab, Clara.io\n\n`;

          responseText += `## Lưu ý khi chuyển đổi\n\n`;
          responseText += `- Có thể mất một số thông tin vật liệu và kết cấu\n`;
          responseText += `- Kiểm tra tỷ lệ và đơn vị sau khi chuyển đổi\n`;
          responseText += `- Một số tính năng đặc biệt có thể không được hỗ trợ ở định dạng đích\n`;
        } else if (intent.action === 'open') {
          // Thông tin về cách mở định dạng
          responseText = `# Cách mở định dạng ${format.name}\n\n`;
          responseText += `${format.description}\n\n`;
          responseText += `## Phần mềm hỗ trợ\n\n`;

          format.compatibility.forEach((software, index) => {
            responseText += `${index + 1}. **${software}**\n`;
          });

          responseText += `\n## Hướng dẫn mở file\n\n`;
          responseText += `1. Mở phần mềm tương thích\n`;
          responseText += `2. Sử dụng menu File > Open hoặc Import\n`;
          responseText += `3. Chọn file ${intent.format} cần mở\n`;
          responseText += `4. Điều chỉnh các tùy chọn nhập nếu cần\n`;
          responseText += `5. Xác nhận và đợi quá trình nhập hoàn tất\n`;
        } else if (intent.action === 'compatibility') {
          // Thông tin về tương thích
          responseText = `# Tương thích của định dạng ${format.name}\n\n`;
          responseText += `${format.description}\n\n`;
          responseText += `## Phần mềm tương thích\n\n`;

          format.compatibility.forEach((software, index) => {
            responseText += `${index + 1}. **${software}**\n`;
          });

          responseText += `\n## Ưu điểm\n\n`;
          format.pros.forEach((pro, index) => {
            responseText += `- ${pro}\n`;
          });

          responseText += `\n## Nhược điểm\n\n`;
          format.cons.forEach((con, index) => {
            responseText += `- ${con}\n`;
          });
        } else {
          // Thông tin chung về định dạng
          responseText = `# Thông tin về định dạng ${format.name}\n\n`;
          responseText += `${format.description}\n\n`;

          responseText += `## Tương thích với\n\n`;
          format.compatibility.forEach((software, index) => {
            responseText += `- ${software}\n`;
          });

          responseText += `\n## Ưu điểm\n\n`;
          format.pros.forEach((pro, index) => {
            responseText += `- ${pro}\n`;
          });

          responseText += `\n## Nhược điểm\n\n`;
          format.cons.forEach((con, index) => {
            responseText += `- ${con}\n`;
          });
        }
      } else {
        // Không tìm thấy thông tin về định dạng
        responseText = `Xin lỗi, tôi không có thông tin chi tiết về định dạng ${intent.format.toUpperCase()}. Vui lòng thử lại với một trong các định dạng phổ biến như SKP, FBX, OBJ, 3DS hoặc BLEND.`;
      }
    } else {
      // Thông tin chung về các định dạng file
      responseText = `# Thông tin về các định dạng file 3D phổ biến\n\n`;
      responseText += `Dưới đây là thông tin về các định dạng file 3D phổ biến được hỗ trợ trên 3DSKETCHUP.NET:\n\n`;

      Object.entries(formatInfo).forEach(([key, format]) => {
        responseText += `## ${format.name}\n\n`;
        responseText += `${format.description}\n\n`;
        responseText += `**Tương thích với**: ${format.compatibility.join(', ')}\n\n`;
      });

      responseText += `Bạn có thể hỏi thêm thông tin chi tiết về từng định dạng cụ thể.`;
    }

    return {
      text: responseText
    };
  } catch (error) {
    console.error('Error handling file format intent:', error);
    return {
      text: 'Xin lỗi, tôi không thể cung cấp thông tin về định dạng file lúc này. Vui lòng thử lại sau.'
    };
  }
};

/**
 * Xử lý ý định hỗ trợ kỹ thuật
 * @param {Object} intent - Ý định được phát hiện
 * @returns {Promise<Object>} - Kết quả xử lý
 */
const handleTechnicalSupportIntent = async (intent) => {
  try {
    // Các vấn đề kỹ thuật phổ biến và giải pháp
    const technicalIssues = {
      'download': {
        title: 'Vấn đề tải xuống',
        symptoms: ['Không thể tải xuống mô hình', 'Tải xuống bị gián đoạn', 'File tải về bị lỗi'],
        solutions: [
          'Đảm bảo bạn đã đăng nhập vào tài khoản',
          'Kiểm tra kết nối internet của bạn',
          'Xóa cache trình duyệt và thử lại',
          'Sử dụng trình duyệt khác (Chrome, Firefox, Edge)',
          'Kiểm tra xem gói đăng ký của bạn có cho phép tải xuống mô hình này không',
          'Liên hệ hỗ trợ nếu vấn đề vẫn tiếp diễn'
        ]
      },
      'viewer': {
        title: 'Vấn đề với trình xem mô hình 3D',
        symptoms: ['Không hiển thị mô hình', 'Mô hình hiển thị không đúng', 'Trình xem bị treo'],
        solutions: [
          'Đảm bảo trình duyệt của bạn hỗ trợ WebGL',
          'Cập nhật trình duyệt lên phiên bản mới nhất',
          'Tắt các tiện ích mở rộng có thể gây xung đột',
          'Giảm chất lượng hiển thị trong cài đặt trình xem',
          'Thử tải lại trang',
          'Sử dụng máy tính có card đồ họa tốt hơn'
        ]
      },
      'account': {
        title: 'Vấn đề tài khoản',
        symptoms: ['Không thể đăng nhập', 'Quên mật khẩu', 'Không nhận được email xác nhận'],
        solutions: [
          'Sử dụng tính năng "Quên mật khẩu" để đặt lại mật khẩu',
          'Kiểm tra thư mục spam/junk trong email của bạn',
          'Đảm bảo bạn đang sử dụng email đã đăng ký',
          'Xóa cookie và thử đăng nhập lại',
          'Liên hệ hỗ trợ nếu vấn đề vẫn tiếp diễn'
        ]
      },
      'payment': {
        title: 'Vấn đề thanh toán',
        symptoms: ['Thanh toán không thành công', 'Bị tính phí nhưng không được nâng cấp', 'Không thể hủy đăng ký'],
        solutions: [
          'Kiểm tra thông tin thẻ tín dụng của bạn',
          'Đảm bảo thẻ của bạn hỗ trợ giao dịch quốc tế',
          'Kiểm tra email xác nhận thanh toán',
          'Đợi 24 giờ để hệ thống xử lý thanh toán',
          'Liên hệ ngân hàng của bạn nếu thấy giao dịch bất thường',
          'Liên hệ hỗ trợ với mã giao dịch (nếu có)'
        ]
      }
    };

    // Phân tích nội dung câu hỏi để xác định vấn đề
    const message = intent.message || '';
    let issueType = null;

    if (message.includes('tải') || message.includes('download') || message.includes('không tải được')) {
      issueType = 'download';
    } else if (message.includes('xem') || message.includes('viewer') || message.includes('hiển thị')) {
      issueType = 'viewer';
    } else if (message.includes('đăng nhập') || message.includes('tài khoản') || message.includes('account') || message.includes('mật khẩu')) {
      issueType = 'account';
    } else if (message.includes('thanh toán') || message.includes('payment') || message.includes('nâng cấp') || message.includes('hủy gói')) {
      issueType = 'payment';
    }

    let responseText = '';

    if (issueType && technicalIssues[issueType]) {
      const issue = technicalIssues[issueType];

      responseText = `# ${issue.title}\n\n`;
      responseText += `## Các triệu chứng phổ biến\n\n`;

      issue.symptoms.forEach((symptom, index) => {
        responseText += `- ${symptom}\n`;
      });

      responseText += `\n## Giải pháp đề xuất\n\n`;

      issue.solutions.forEach((solution, index) => {
        responseText += `${index + 1}. ${solution}\n`;
      });

      responseText += `\n## Cần thêm hỗ trợ?\n\n`;
      responseText += `Nếu các giải pháp trên không giải quyết được vấn đề của bạn, vui lòng liên hệ với đội hỗ trợ của chúng tôi <NAME_EMAIL> hoặc sử dụng biểu mẫu liên hệ trên trang web.`;
    } else {
      // Thông tin hỗ trợ kỹ thuật chung
      responseText = `# Hỗ trợ kỹ thuật\n\n`;
      responseText += `Dưới đây là một số vấn đề kỹ thuật phổ biến và cách khắc phục:\n\n`;

      Object.values(technicalIssues).forEach((issue) => {
        responseText += `## ${issue.title}\n\n`;
        responseText += `**Giải pháp nhanh**: ${issue.solutions[0]}\n\n`;
      });

      responseText += `## Cần thêm hỗ trợ?\n\n`;
      responseText += `Vui lòng mô tả chi tiết vấn đề bạn đang gặp phải, hoặc liên hệ với đội hỗ trợ của chúng tôi <NAME_EMAIL>.`;
    }

    return {
      text: responseText
    };
  } catch (error) {
    console.error('Error handling technical support intent:', error);
    return {
      text: 'Xin lỗi, tôi không thể cung cấp hỗ trợ kỹ thuật lúc này. Vui lòng liên hệ với đội hỗ trợ của chúng tôi <NAME_EMAIL>.'
    };
  }
};

/**
 * Xử lý ý định hướng dẫn tải xuống
 * @param {Object} intent - Ý định được phát hiện
 * @returns {Promise<Object>} - Kết quả xử lý
 */
const handleDownloadHelpIntent = async (intent) => {
  try {
    const responseText = `# Hướng dẫn tải xuống mô hình 3D\n\n`;

    responseText += `## Các bước tải xuống mô hình\n\n`;
    responseText += `1. **Đăng nhập vào tài khoản** - Đảm bảo bạn đã đăng nhập vào tài khoản 3DSKETCHUP.NET của mình\n`;
    responseText += `2. **Tìm mô hình** - Sử dụng thanh tìm kiếm hoặc duyệt qua các danh mục để tìm mô hình bạn muốn\n`;
    responseText += `3. **Xem trang chi tiết mô hình** - Nhấp vào mô hình để xem thông tin chi tiết\n`;
    responseText += `4. **Kiểm tra yêu cầu** - Đảm bảo gói đăng ký của bạn cho phép tải xuống mô hình này\n`;
    responseText += `5. **Nhấp vào nút "Tải xuống"** - Nút này thường nằm ở phía trên bên phải trang\n`;
    responseText += `6. **Chọn định dạng file** - Nếu có nhiều định dạng, chọn định dạng phù hợp với phần mềm của bạn\n`;
    responseText += `7. **Xác nhận tải xuống** - Nhấp vào nút xác nhận để bắt đầu tải xuống\n`;
    responseText += `8. **Lưu file** - Chọn vị trí lưu file trên máy tính của bạn\n\n`;

    responseText += `## Các vấn đề phổ biến khi tải xuống\n\n`;
    responseText += `### Không thể tải xuống mô hình\n\n`;
    responseText += `- Đảm bảo bạn đã đăng nhập\n`;
    responseText += `- Kiểm tra xem gói đăng ký của bạn có cho phép tải xuống mô hình này không\n`;
    responseText += `- Kiểm tra số lượt tải xuống còn lại trong tháng\n`;
    responseText += `- Xóa cache trình duyệt và thử lại\n\n`;

    responseText += `### Tải xuống bị gián đoạn\n\n`;
    responseText += `- Kiểm tra kết nối internet của bạn\n`;
    responseText += `- Sử dụng trình duyệt khác (Chrome, Firefox, Edge)\n`;
    responseText += `- Tắt các tiện ích mở rộng có thể gây xung đột\n`;
    responseText += `- Thử tải xuống vào thời điểm khác\n\n`;

    responseText += `### File tải về bị lỗi\n\n`;
    responseText += `- Thử tải xuống lại\n`;
    responseText += `- Sử dụng phần mềm giải nén khác nếu file là định dạng nén\n`;
    responseText += `- Kiểm tra xem bạn có đủ dung lượng ổ đĩa không\n`;
    responseText += `- Liên hệ hỗ trợ nếu vấn đề vẫn tiếp diễn\n\n`;

    responseText += `## Cần thêm hỗ trợ?\n\n`;
    responseText += `Nếu bạn vẫn gặp vấn đề khi tải xuống, vui lòng liên hệ với đội hỗ trợ của chúng tôi <NAME_EMAIL> hoặc sử dụng biểu mẫu liên hệ trên trang web.`;

    return {
      text: responseText
    };
  } catch (error) {
    console.error('Error handling download help intent:', error);
    return {
      text: 'Xin lỗi, tôi không thể cung cấp hướng dẫn tải xuống lúc này. Vui lòng thử lại sau hoặc liên hệ với đội hỗ trợ của chúng tôi.'
    };
  }
};

// Get rate limit status - always returns not rate limited
export const getRateLimitStatus = async (req, res) => {
  // Rate limiting has been disabled as per user request
  return res.json({
    success: true,
    requestsInWindow: 0,
    maxRequests: 1000, // Set a very high number
    windowSizeSeconds: 60,
    isRateLimited: false // Always return false
  });
};

// @desc    Upload and analyze image for chat
// @route   POST /api/chat/upload-image
// @access  Public
export const uploadAndAnalyzeImage = async (req, res) => {
  try {
    console.log('📸 Image upload and analysis request received');

    // Check if image file exists
    if (!req.files || !req.files.image) {
      return res.status(400).json({
        success: false,
        error: 'No image file provided'
      });
    }

    const imageFile = req.files.image;
    console.log(`📸 Processing image: ${imageFile.name}, size: ${imageFile.size} bytes`);

    // Validate image
    const validation = imageProcessor.validateImage(imageFile);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: validation.errors.join(', ')
      });
    }

    // Get image data - handle both buffer and temp file
    let imageData;
    if (imageFile.data && imageFile.data.length > 0) {
      imageData = imageFile.data;
    } else if (imageFile.tempFilePath) {
      const fs = await import('fs');
      imageData = await fs.promises.readFile(imageFile.tempFilePath);
    } else {
      throw new Error('No image data available');
    }

    console.log('📸 Image data length:', imageData.length);

    // Use ImageSearchService for complete analysis
    const { language = 'vi' } = req.body;
    const result = await imageSearchService.analyzeImageForModels(imageData, language);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to analyze image with AI',
        details: result.error
      });
    }

    // Generate unique filename and save processed image for display
    const filename = imageProcessor.generateUniqueFilename(imageFile.name, 'chat_img');
    const processedImage = await imageProcessor.processImage(imageData, {
      width: 512,
      height: 512,
      quality: 80,
      format: 'jpeg'
    });

    await imageProcessor.saveImage(
      processedImage.buffer,
      filename,
      './uploads/images/chat'
    );

    console.log('✅ Image analysis completed successfully');

    res.status(200).json({
      success: true,
      data: {
        imageUrl: `/uploads/images/chat/${filename}`,
        analysis: result.data.analysis,
        relatedModels: result.data.relatedModels,
        response: result.data.response,
        metadata: {
          originalSize: imageFile.size,
          processedSize: result.data.metadata.processedImageSize,
          totalModelsFound: result.data.metadata.totalModelsFound
        }
      }
    });

  } catch (error) {
    console.error('❌ Image upload and analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process and analyze image',
      details: error.message
    });
  }
};

/**
 * Find related 3D models based on image analysis
 */
async function findRelatedModelsFromImageAnalysis(analysis) {
  try {
    const searchTerms = [
      ...analysis.elements || [],
      ...analysis.objects || [],
      ...analysis.categories || [],
      analysis.style || ''
    ].filter(term => term && term.length > 0);

    if (searchTerms.length === 0) {
      return [];
    }

    // Create search query for MongoDB
    const searchQuery = {
      $or: [
        { title: { $regex: searchTerms.join('|'), $options: 'i' } },
        { description: { $regex: searchTerms.join('|'), $options: 'i' } },
        { tags: { $in: searchTerms.map(term => new RegExp(term, 'i')) } },
        { category: { $regex: searchTerms.join('|'), $options: 'i' } }
      ],
      status: 'active'
    };

    const models = await Model.find(searchQuery)
      .populate('createdBy', 'name')
      .populate('category')
      .sort({ downloads: -1, rating: -1 })
      .limit(10)
      .lean();

    return models;

  } catch (error) {
    console.error('Error finding related models:', error);
    return [];
  }
}

/**
 * Generate contextual response based on image analysis and related models
 */
async function generateImageContextualResponse(analysis, relatedModels, language = 'vi') {
  try {
    const isEnglish = language === 'en-US';

    if (relatedModels.length === 0) {
      return isEnglish
        ? "I can see the image you've shared! While I couldn't find exact matching models in our database, I can help you search for specific elements or suggest similar designs. What particular aspect of this image interests you most?"
        : "Tôi có thể thấy hình ảnh bạn đã chia sẻ! Mặc dù tôi không thể tìm thấy các mô hình phù hợp chính xác trong cơ sở dữ liệu của chúng tôi, tôi có thể giúp bạn tìm kiếm các yếu tố cụ thể hoặc đề xuất các thiết kế tương tự. Khía cạnh nào của hình ảnh này khiến bạn quan tâm nhất?";
    }

    const modelTitles = relatedModels.slice(0, 3).map(model => model.title).join(', ');
    const elements = analysis.elements?.slice(0, 3).join(', ') || '';
    const objects = analysis.objects?.slice(0, 3).join(', ') || '';

    if (isEnglish) {
      return `Great image! I can see ${elements ? `architectural elements like ${elements}` : 'interesting design elements'}${objects ? ` and objects such as ${objects}` : ''}. Based on what I see, I found ${relatedModels.length} related 3D models in our collection, including "${modelTitles}". These models could help you recreate similar designs. Would you like me to show you more details about any of these models, or help you search for something specific?`;
    } else {
      return `Hình ảnh tuyệt vời! Tôi có thể thấy ${elements ? `các yếu tố kiến trúc như ${elements}` : 'các yếu tố thiết kế thú vị'}${objects ? ` và các đồ vật như ${objects}` : ''}. Dựa trên những gì tôi thấy, tôi đã tìm thấy ${relatedModels.length} mô hình 3D liên quan trong bộ sưu tập của chúng tôi, bao gồm "${modelTitles}". Những mô hình này có thể giúp bạn tái tạo các thiết kế tương tự. Bạn có muốn tôi hiển thị thêm chi tiết về bất kỳ mô hình nào trong số này, hoặc giúp bạn tìm kiếm thứ gì đó cụ thể không?`;
    }

  } catch (error) {
    console.error('Error generating contextual response:', error);
    const isEnglish = language === 'en-US';
    return isEnglish
      ? "I can see your image! Let me help you find related 3D models or answer any questions about what you're looking for."
      : "Tôi có thể thấy hình ảnh của bạn! Hãy để tôi giúp bạn tìm các mô hình 3D liên quan hoặc trả lời bất kỳ câu hỏi nào về những gì bạn đang tìm kiếm.";
  }
}