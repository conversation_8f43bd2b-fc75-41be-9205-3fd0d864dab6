import React from 'react';
import { motion } from 'framer-motion';
import { FiUsers, FiTarget, FiAward, FiTrendingUp, FiGlobe, FiCode } from 'react-icons/fi';
import Header from '../components/Header';
import Footer from '../components/Footer';
import PageTransition from '../components/PageTransition';

const About = () => {
  // Team members data
  const teamMembers = [
    {
      name: '<PERSON>',
      role: 'Founder & CEO',
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      bio: '<PERSON> has over 15 years of experience in 3D modeling and architectural visualization.'
    },
    {
      name: '<PERSON>',
      role: 'Chief Technology Officer',
      image: 'https://randomuser.me/api/portraits/women/44.jpg',
      bio: '<PERSON> leads our development team with expertise in web technologies and 3D rendering engines.'
    },
    {
      name: '<PERSON>',
      role: 'Creative Director',
      image: 'https://randomuser.me/api/portraits/men/67.jpg',
      bio: '<PERSON> oversees the quality and artistic direction of all 3D models on our platform.'
    },
    {
      name: '<PERSON>',
      role: 'Head of Customer Success',
      image: 'https://randomuser.me/api/portraits/women/28.jpg',
      bio: 'Emily ensures our users have the best experience with our platform and services.'
    }
  ];

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />
      
      <PageTransition>
        <main className="flex-grow pt-24 pb-16">
          {/* Hero Section */}
          <section className="container mx-auto px-4 mb-16">
            <div className="max-w-4xl mx-auto text-center">
              <motion.h1 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6"
              >
                About 3DSKETCHUP.NET
              </motion.h1>
              
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-xl text-gray-600 dark:text-gray-300 mb-8"
              >
                Your premier destination for high-quality 3D models, empowering creators and professionals worldwide.
              </motion.p>
              
              <motion.div 
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="relative h-80 md:h-96 rounded-xl overflow-hidden shadow-xl"
              >
                <img 
                  src="https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                  alt="3D modeling workspace" 
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                  <div className="p-6 text-white">
                    <p className="text-lg font-medium">Established in 2020</p>
                    <p>Serving over 50,000 designers and architects worldwide</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </section>
          
          {/* Our Mission */}
          <section className="container mx-auto px-4 py-12 mb-16">
            <div className="max-w-4xl mx-auto">
              <div className="flex flex-col md:flex-row items-center gap-8">
                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="md:w-1/2"
                >
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Mission</h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    At 3DSKETCHUP.NET, our mission is to democratize access to high-quality 3D models and resources, 
                    enabling creators of all skill levels to bring their visions to life without technical barriers.
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">
                    We believe that everyone should have access to the tools and resources they need to create 
                    stunning 3D visualizations, whether for professional projects, educational purposes, or personal creativity.
                  </p>
                </motion.div>
                
                <motion.div 
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5 }}
                  className="md:w-1/2 bg-blue-600 text-white p-8 rounded-xl"
                >
                  <div className="flex items-center mb-4">
                    <FiTarget className="h-8 w-8 mr-3" />
                    <h3 className="text-xl font-bold">Our Vision</h3>
                  </div>
                  <p className="mb-4">
                    To become the world's leading platform for 3D content, where creators can find, share, and 
                    collaborate on models that push the boundaries of digital design.
                  </p>
                  <div className="flex items-center mb-4">
                    <FiAward className="h-8 w-8 mr-3" />
                    <h3 className="text-xl font-bold">Our Values</h3>
                  </div>
                  <ul className="list-disc list-inside">
                    <li>Quality over quantity</li>
                    <li>Accessibility and inclusivity</li>
                    <li>Creator empowerment</li>
                    <li>Continuous innovation</li>
                  </ul>
                </motion.div>
              </div>
            </div>
          </section>
          
          {/* What We Offer */}
          <section className="bg-white dark:bg-gray-800 py-16 mb-16">
            <div className="container mx-auto px-4">
              <div className="max-w-4xl mx-auto text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">What We Offer</h2>
                <p className="text-xl text-gray-600 dark:text-gray-300">
                  Comprehensive solutions for all your 3D modeling needs
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[
                  {
                    icon: <FiGlobe className="h-10 w-10 text-blue-600" />,
                    title: "Extensive Model Library",
                    description: "Access thousands of high-quality 3D models across various categories, from architecture to product design."
                  },
                  {
                    icon: <FiCode className="h-10 w-10 text-blue-600" />,
                    title: "Multiple Format Support",
                    description: "Download models in various formats including OBJ, FBX, GLTF, and STL to suit your workflow."
                  },
                  {
                    icon: <FiUsers className="h-10 w-10 text-blue-600" />,
                    title: "Creator Community",
                    description: "Join a vibrant community of designers, architects, and 3D enthusiasts to share ideas and get inspired."
                  },
                  {
                    icon: <FiTrendingUp className="h-10 w-10 text-blue-600" />,
                    title: "Regular Updates",
                    description: "Our library is constantly growing with new models added weekly to keep up with the latest trends."
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-md"
                  >
                    <div className="flex flex-col items-center text-center">
                      <div className="mb-4">{item.icon}</div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{item.title}</h3>
                      <p className="text-gray-600 dark:text-gray-300">{item.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
          
          {/* Our Team */}
          <section className="container mx-auto px-4 py-12 mb-16">
            <div className="max-w-4xl mx-auto text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Meet Our Team</h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                The passionate professionals behind 3DSKETCHUP.NET
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembers.map((member, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden"
                >
                  <img 
                    src={member.image} 
                    alt={member.name} 
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">{member.name}</h3>
                    <p className="text-blue-600 dark:text-blue-400 mb-2">{member.role}</p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">{member.bio}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </section>
        </main>
      </PageTransition>
      
      <Footer />
    </div>
  );
};

export default About;
