import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useModel } from '../context/ModelContext';
import Header from '../components/Header';
import Footer from '../components/Footer';
import ModelCard from '../components/ModelCard';
import Pagination from '../components/Pagination';
import FilterSidebar from '../components/FilterSidebar';
import { FiFilter, FiX, FiSearch } from 'react-icons/fi';
import { motion } from 'framer-motion';

const SearchResults = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const query = searchParams.get('q') || '';

  const { models, categories, loading } = useModel();
  const [filteredModels, setFilteredModels] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    subcategory: [],
    format: [],
    isPremium: null,
    minRating: 0,
    maxRating: 5,
    sortBy: 'relevance'
  });

  const modelsPerPage = 12;

  // Filter models based on search query and other filters
  useEffect(() => {
    if (!models || models.length === 0 || !query) return;

    // Basic search implementation
    let result = models.filter(model => {
      const searchFields = [
        model.title,
        model.description,
        model.category,
        model.subcategory,
        model.format,
        ...(model.tags || [])
      ].map(field => (field || '').toLowerCase());

      const searchTerms = query.toLowerCase().split(' ');

      // Check if any search term is found in any field
      return searchTerms.some(term =>
        searchFields.some(field => field.includes(term))
      );
    });

    // Apply additional filters
    if (filters.subcategory.length > 0) {
      result = result.filter(model => filters.subcategory.includes(model.subcategory));
    }

    if (filters.format.length > 0) {
      result = result.filter(model => filters.format.includes(model.format));
    }

    if (filters.isPremium !== null) {
      result = result.filter(model => model.isPremium === filters.isPremium);
    }

    result = result.filter(model =>
      parseFloat(model.rating) >= filters.minRating &&
      parseFloat(model.rating) <= filters.maxRating
    );

    // Sort results
    switch(filters.sortBy) {
      case 'relevance':
        // Keep default order (which is by relevance)
        break;
      case 'newest':
        result.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        break;
      case 'popular':
        result.sort((a, b) => b.downloads - a.downloads);
        break;
      case 'rating':
        result.sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating));
        break;
      default:
        break;
    }

    setFilteredModels(result);
    setCurrentPage(1); // Reset to first page when search changes
  }, [query, models, filters]);

  // Get current models for pagination
  const indexOfLastModel = currentPage * modelsPerPage;
  const indexOfFirstModel = indexOfLastModel - modelsPerPage;
  const currentModels = filteredModels.slice(indexOfFirstModel, indexOfLastModel);

  // Change page
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Toggle filter sidebar on mobile
  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="flex-grow container mx-auto px-4 pt-24 pb-12">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Mobile filter toggle */}
          <div className="md:hidden flex justify-between items-center mb-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
              <FiSearch className="mr-2" />
              Search Results
            </h1>
            <button
              onClick={toggleFilter}
              className="btn btn-secondary flex items-center"
            >
              <FiFilter className="mr-2" />
              Filters
            </button>
          </div>

          {/* Filter sidebar - desktop */}
          <div className="hidden md:block w-64 flex-shrink-0">
            <div className="sticky top-24">
              <FilterSidebar
                filters={filters}
                onFilterChange={handleFilterChange}
                categories={categories}
              />
            </div>
          </div>

          {/* Filter sidebar - mobile */}
          {isFilterOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden">
              <motion.div
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ duration: 0.3 }}
                className="absolute right-0 top-0 h-full w-80 bg-white dark:bg-gray-800 p-4 overflow-y-auto"
              >
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Filters</h3>
                  <button onClick={toggleFilter} className="text-gray-500">
                    <FiX className="h-6 w-6" />
                  </button>
                </div>
                <FilterSidebar
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  categories={categories}
                  onClose={toggleFilter}
                />
              </motion.div>
            </div>
          )}

          {/* Main content */}
          <div className="flex-grow">
            <div className="hidden md:flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <FiSearch className="mr-2" />
                Search Results for "{query}"
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {filteredModels.length} models found
              </p>
            </div>

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="loader"></div>
              </div>
            ) : filteredModels.length > 0 ? (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {currentModels.map(model => (
                    <ModelCard key={model.id} model={model} />
                  ))}
                </div>

                <Pagination
                  itemsPerPage={modelsPerPage}
                  totalItems={filteredModels.length}
                  paginate={paginate}
                  currentPage={currentPage}
                />
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <p className="text-xl text-gray-600 dark:text-gray-400 mb-4">
                  No models found matching "{query}" with the selected filters.
                </p>
                <button
                  onClick={() => setFilters({
                    subcategory: [],
                    format: [],
                    isPremium: null,
                    minRating: 0,
                    maxRating: 5,
                    sortBy: 'relevance'
                  })}
                  className="btn btn-primary"
                >
                  Reset Filters
                </button>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SearchResults;
