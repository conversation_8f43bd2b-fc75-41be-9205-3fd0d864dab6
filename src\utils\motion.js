/**
 * Framer Motion Wrapper
 *
 * This file provides a wrapper around framer-motion to ensure it uses the same React instance
 * as the rest of the application.
 */

import React from 'react';
import * as FramerMotion from 'framer-motion';

// Re-export all framer-motion exports
export const {
  motion,
  AnimatePresence,
  useAnimation,
  useMotionValue,
  useTransform,
  useSpring,
  useScroll,
  useInView,
  useAnimationControls,
  useMotionTemplate,
  useVelocity,
  useTime,
  usePresence,
  useWillChange,
  useReducedMotion,
  useViewportScroll,
  useDragControls,
  useAnimationFrame,
  useIsPresent,
  useCycle,
  useMotionValueEvent,
  useForceUpdate,
  useInstantTransition,
  useIsomorphicLayoutEffect,
  useUnmountEffect,
  useAnimatedState,
  createMotionComponent,
  m,
  AnimateSharedLayout,
  LayoutGroup,
  MotionConfig,
  LazyMotion,
  domAnimation,
  domMax,
  domAnimate,
  MotionValue,
  animate,
  useTransformValues,
  transform,
  isValidMotionProp,
  useMotionProps,
  useAnimationState
} = FramerMotion;

// Create a custom PageTransition component that uses our React instance
export const PageTransition = ({
  children,
  variant = 'fadeUp',
  transitionType = 'smooth',
  className = '',
  style = {},
  layoutId = null,
}) => {
  // Animation variants for page transitions
  const TRANSITION_VARIANTS = {
    // Default fade up transition
    fadeUp: {
      initial: {
        opacity: 0,
        y: 20,
      },
      in: {
        opacity: 1,
        y: 0,
      },
      out: {
        opacity: 0,
        y: -20,
      },
    },

    // Fade transition without movement
    fade: {
      initial: {
        opacity: 0,
      },
      in: {
        opacity: 1,
      },
      out: {
        opacity: 0,
      },
    },

    // Slide in from right
    slideRight: {
      initial: {
        opacity: 0,
        x: -20,
      },
      in: {
        opacity: 1,
        x: 0,
      },
      out: {
        opacity: 0,
        x: 20,
      },
    },

    // Slide in from left
    slideLeft: {
      initial: {
        opacity: 0,
        x: 20,
      },
      in: {
        opacity: 1,
        x: 0,
      },
      out: {
        opacity: 0,
        x: -20,
      },
    },

    // Scale transition
    scale: {
      initial: {
        opacity: 0,
        scale: 0.95,
      },
      in: {
        opacity: 1,
        scale: 1,
      },
      out: {
        opacity: 0,
        scale: 0.95,
      },
    },

    // Flip transition
    flip: {
      initial: {
        opacity: 0,
        rotateX: 90,
        perspective: 1000,
      },
      in: {
        opacity: 1,
        rotateX: 0,
      },
      out: {
        opacity: 0,
        rotateX: -90,
      },
    },
  };

  // Transition settings
  const TRANSITION_TYPES = {
    // Default smooth transition
    smooth: {
      type: 'tween',
      ease: 'easeInOut',
      duration: 0.4,
    },

    // Spring transition with bounce
    spring: {
      type: 'spring',
      stiffness: 300,
      damping: 30,
    },

    // Quick transition
    quick: {
      type: 'tween',
      ease: 'easeOut',
      duration: 0.2,
    },

    // Slow, dramatic transition
    dramatic: {
      type: 'tween',
      ease: 'easeInOut',
      duration: 0.8,
    },
  };

  // Determine which variant to use
  const selectedVariant = typeof variant === 'string'
    ? TRANSITION_VARIANTS[variant] || TRANSITION_VARIANTS.fadeUp
    : variant;

  // Determine which transition to use
  const selectedTransition = typeof transitionType === 'string'
    ? TRANSITION_TYPES[transitionType] || TRANSITION_TYPES.smooth
    : transitionType;

  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={selectedVariant}
      transition={selectedTransition}
      className={className}
      style={style}
      layoutId={layoutId}
    >
      {children}
    </motion.div>
  );
};
