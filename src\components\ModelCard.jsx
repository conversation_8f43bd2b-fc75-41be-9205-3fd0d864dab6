import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiDownload, FiEye, FiStar } from 'react-icons/fi';
import ImageWithFallback from './ui/ImageWithFallback';

const ModelCard = ({ model }) => {
  // Get the correct ID (MongoDB uses _id, others use id)
  const modelId = model._id || model.id;

  // Don't render if no valid ID or ID is too short (MongoDB ObjectId should be 24 chars)
  if (!modelId || modelId.length < 12) {
    console.warn('ModelCard: Invalid ID found for model:', modelId, model);
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-all hover:shadow-lg"
    >
      <Link to={`/model/${modelId}`} className="block relative pb-[75%] overflow-hidden">
        <ImageWithFallback
          src={model.imageUrl}
          alt={model.title}
          className="absolute inset-0 w-full h-full object-cover transition-transform hover:scale-105"
          lowResSrc="/images/placeholder-tiny.jpg"
        />
        {model.isPremium && (
          <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded">
            Premium
          </div>
        )}
      </Link>

      <div className="p-4">
        <Link to={`/model/${modelId}`}>
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white truncate hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            {model.title}
          </h3>
        </Link>

        <div className="flex items-center mt-1 text-sm text-gray-600 dark:text-gray-400">
          <Link to={`/category/${model.category.toLowerCase()}`} className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
            {model.category}
          </Link>
          {model.subcategory && (
            <Link to={`/subcategory/${model.subcategory.toLowerCase()}`} className="ml-2 hover:text-blue-600 dark:hover:text-blue-400">
              {model.subcategory}
            </Link>
          )}
        </div>

        <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {model.description && (
            <p className="line-clamp-2">{model.description}</p>
          )}
        </div>

        <div className="mt-3 flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center text-xs text-gray-500 dark:text-gray-400">
              <FiStar className="w-3.5 h-3.5 mr-1 text-yellow-500" />
              {model.rating || '4.5'}
            </span>

            <span className="inline-flex items-center text-xs text-gray-500 dark:text-gray-400">
              <FiDownload className="w-3.5 h-3.5 mr-1" />
              {model.downloads || '234'}
            </span>

            <span className="inline-flex items-center text-xs text-gray-500 dark:text-gray-400">
              <FiEye className="w-3.5 h-3.5 mr-1" />
              {model.views || '1.2k'}
            </span>
          </div>
        </div>

        <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700 flex justify-between items-center">
          <span className="text-sm font-medium text-gray-600 dark:text-gray-400">{model.format}</span>
          <Link
            to={`/model/${modelId}`}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
          >
            View Details
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default ModelCard;
