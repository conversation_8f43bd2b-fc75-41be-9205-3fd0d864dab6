import React from 'react';
import { FiChevronDown } from 'react-icons/fi';

const FilterSidebar = ({ filters, onFilterChange, categories, onClose }) => {
  // Available subcategories
  const subcategories = [
    'Interior',
    'Exterior',
    'Kitchen',
    'Bathroom',
    'Living Room',
    'Bedroom',
    'Office',
    'Garden',
    'Landscape',
    'Furniture',
    'Decoration',
    'Other'
  ];

  // Available formats
  const formats = [
    'Sketchup 2023',
    'Sketchup 2022',
    'Sketchup 2021',
    'Sketchup 2020',
    'Sketchup 2019',
    'Sketchup 2018',
    'Sketchup 2017',
    'Sketchup 8',
    'Sketchup 7',
    'FBX',
    'OBJ',
    'DAE',
    '3DS',
    'MAX'
  ];

  // Handle checkbox change for array filters
  const handleArrayFilterChange = (filterName, value) => {
    const currentValues = [...filters[filterName]];
    const index = currentValues.indexOf(value);
    
    if (index === -1) {
      currentValues.push(value);
    } else {
      currentValues.splice(index, 1);
    }
    
    onFilterChange({
      ...filters,
      [filterName]: currentValues
    });
  };

  // Handle radio button change
  const handleRadioChange = (filterName, value) => {
    onFilterChange({
      ...filters,
      [filterName]: value
    });
  };

  // Handle range change
  const handleRangeChange = (filterName, value) => {
    onFilterChange({
      ...filters,
      [filterName]: value
    });
  };

  // Handle sort change
  const handleSortChange = (value) => {
    onFilterChange({
      ...filters,
      sortBy: value
    });
  };

  // Reset all filters
  const resetFilters = () => {
    onFilterChange({
      subcategory: [],
      format: [],
      isPremium: null,
      minRating: 0,
      maxRating: 5,
      sortBy: 'newest'
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Filters</h3>
        <button 
          onClick={resetFilters}
          className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          Reset All
        </button>
      </div>

      {/* Sort By */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Sort By
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="sortBy"
              checked={filters.sortBy === 'newest'}
              onChange={() => handleSortChange('newest')}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Newest</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="sortBy"
              checked={filters.sortBy === 'popular'}
              onChange={() => handleSortChange('popular')}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Most Popular</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="sortBy"
              checked={filters.sortBy === 'rating'}
              onChange={() => handleSortChange('rating')}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Highest Rated</span>
          </label>
        </div>
      </div>

      {/* Subcategory Filter */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Subcategory
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {subcategories.map(subcategory => (
            <label key={subcategory} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.subcategory.includes(subcategory)}
                onChange={() => handleArrayFilterChange('subcategory', subcategory)}
                className="form-checkbox text-blue-600"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">{subcategory}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Format Filter */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Format
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {formats.map(format => (
            <label key={format} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.format.includes(format)}
                onChange={() => handleArrayFilterChange('format', format)}
                className="form-checkbox text-blue-600"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">{format}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Premium Filter */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Type
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="isPremium"
              checked={filters.isPremium === null}
              onChange={() => handleRadioChange('isPremium', null)}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">All</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="isPremium"
              checked={filters.isPremium === false}
              onChange={() => handleRadioChange('isPremium', false)}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Free</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="isPremium"
              checked={filters.isPremium === true}
              onChange={() => handleRadioChange('isPremium', true)}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Premium</span>
          </label>
        </div>
      </div>

      {/* Rating Filter */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Rating
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-4">
          <div>
            <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
              Minimum Rating: {filters.minRating}
            </label>
            <input
              type="range"
              min="0"
              max="5"
              step="0.5"
              value={filters.minRating}
              onChange={(e) => handleRangeChange('minRating', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
              Maximum Rating: {filters.maxRating}
            </label>
            <input
              type="range"
              min="0"
              max="5"
              step="0.5"
              value={filters.maxRating}
              onChange={(e) => handleRangeChange('maxRating', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Apply button for mobile */}
      {onClose && (
        <button
          onClick={onClose}
          className="w-full btn btn-primary mt-4"
        >
          Apply Filters
        </button>
      )}
    </div>
  );
};

export default FilterSidebar;
